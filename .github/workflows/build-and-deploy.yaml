name: "CI/CD to Bluehost VPS"  

on:  
  push:  
    branches:  
      - master  
      - main  
      - develop  

jobs:  
  build-and-deploy:  
    runs-on: ubuntu-latest  

    steps:  
      - name: "Checkout"  
        uses: actions/checkout@v3  

      - name: "Use Node 18"  
        uses: actions/setup-node@v3  
        with:  
          node-version: 18  

      - name: "Install dependencies"  
        run: |  
          yarn install  

      - name: "Build the application"  
        run: |  
          yarn run build  

      - name: "Prepare VPS directory"  
        uses: appleboy/ssh-action@v1.2.1  
        with:  
          host: ${{ secrets.VPS_HOST }}  
          username: ${{ secrets.VPS_USER }}  
          password: ${{ secrets.VPS_PASSWORD }}  
          port: ${{ secrets.VPS_SSH_PORT }}  
          script_stop: true  
          script: |  
            mkdir -p /var/www/defi-safety-api  
            rm -rf /var/www/defi-safety-api/*  

      - name: "Secure copy to VPS (SCP)"  
        uses: appleboy/scp-action@v0.1.7  
        with:  
          host: ${{ secrets.VPS_HOST }}  
          username: ${{ secrets.VPS_USER }}  
          password: ${{ secrets.VPS_PASSWORD }}  
          port: ${{ secrets.VPS_SSH_PORT }}  
          strip_components: 0  
          source: "dist/,src/,package.json,yarn.lock,.env.example,ormconfig.ts,tsconfig.json,tsconfig.build.json"  
          target: "/var/www/defi-safety-api"  

      - name: "Setup and Deploy"  
        uses: appleboy/ssh-action@v1.2.1  
        with:  
          host: ${{ secrets.VPS_HOST }}  
          username: ${{ secrets.VPS_USER }}  
          password: ${{ secrets.VPS_PASSWORD }}  
          port: ${{ secrets.VPS_SSH_PORT }}  
          script: |  
            cd /var/www/defi-safety-api  

            # Install Node.js 18  
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -  
            sudo apt-get install -y nodejs  

            # Install global packages  
            sudo npm install -g yarn typescript ts-node  

            # Install dependencies  
            yarn install  

            # Copy environment file if not exists  
            if [ ! -f ".env" ]; then  
              cp .env.example .env  
            fi  

            # Run TypeORM migrations  
            echo "Running database migrations..."  
            yarn run typeorm migration:run -c internalDb || {  
              echo "Migration failed. Checking logs..."  
              cat yarn-error.log || true  
              exit 1  
            }  

            # Install PM2 if not installed  
            sudo npm install -g pm2  

            # Stop existing PM2 process if any  
            pm2 delete "defi-safety-api" || true  

            # Start the application with PM2  
            pm2 start dist/src/main.js --name "defi-safety-api"  

            # Save PM2 configuration  
            pm2 save  

            # Setup PM2 to start on system boot  
            pm2 startup systemd  
            pm2 save  

            # Show running processes  
            pm2 list  

            # Check if the process is running  
            if ! pm2 pid "defi-safety-api" > /dev/null; then  
              echo "Error: Process failed to start!"  
              pm2 logs "defi-safety-api" --lines 50  
              exit 1  
            fi  

            echo "Deployment completed successfully"