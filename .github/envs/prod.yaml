ingress:
  hosts: 
    - api.defisafety.com
  tls:
    - secretName: api-tls
      hosts:
        - api.defisafety.com
envVars:
  NODE_ENV: production
  APP_ENV: production
  APP_NAME: api

  DATABASE_SCHEMA: defisafetyprod
  
  CRYPTO_ARB_DAI_ADDRESS: ******************************************
  CRYPTO_ARB_PROVIDER: https://arb-mainnet.g.alchemy.com/v2/********************************
  CRYPTO_ARB_USDC_ADDRESS: ******************************************
  CRYPTO_ARB_USDT_ADDRESS: ******************************************
  CRYPTO_DEFISAFETY_CONTRACT_ADDRESS: ******************************************
  CRYPTO_ETH_DAI_ADDRESS: ******************************************
  CRYPTO_ETH_PROVIDER: https://eth-mainnet.alchemyapi.io/v2/********************************
  CRYPTO_ETH_USDC_ADDRESS: ******************************************
  CRYPTO_ETH_USDT_ADDRESS: ******************************************
  
  JWT_ACCESS_TOKEN_EXPIRATION_TIME: "120"
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: "604800"
  
  MAIL_HOST: smtp.gmail.com
  MAIL_PORT: "587"
  
  SENTRY_DSN: https://<EMAIL>/3
  SHLVL: "3"
  STRAPI_HOST: https://strapi.defisafety.com

  FRONT_END_HOST: https://www.defisafety.com