ingress:
  hosts:
    - api.dev.defisafety.com
  tls:
    - secretName: api-tls
      hosts:
        - api.dev.defisafety.com
envVars:
  NODE_ENV: production
  APP_ENV: development
  APP_NAME: api

  DATABASE_SCHEMA: defisafetydev

  CRYPTO_ARB_DAI_ADDRESS: '******************************************'
  CRYPTO_ARB_PROVIDER: https://arb-rinkeby.g.alchemy.com/v2/********************************
  CRYPTO_ARB_USDC_ADDRESS: '******************************************'
  CRYPTO_ARB_USDT_ADDRESS: '******************************************'
  CRYPTO_DEFISAFETY_CONTRACT_ADDRESS: '******************************************'
  CRYPTO_ETH_DAI_ADDRESS: '******************************************'
  CRYPTO_ETH_PROVIDER: https://eth-rinkeby.alchemyapi.io/v2/********************************
  CRYPTO_ETH_USDC_ADDRESS: '******************************************'
  CRYPTO_ETH_USDT_ADDRESS: '******************************************'

  JWT_ACCESS_TOKEN_EXPIRATION_TIME: '360'
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: '604800'

  MAIL_HOST: smtp.gmail.com
  MAIL_PORT: '587'

  SENTRY_DSN: https://<EMAIL>/3
  STRAPI_HOST: https://strapi.dev.defisafety.com

  FRONT_END_HOST: https://dev.defisafety.com
  # Temporary key for testing
  DEV_STRIPE_API_KEY: 'sk_test_51KlH03DSASeFUpVD1Ju9Szwm7c9R9hOPUpBD0CeYvtO7VkGVDT6caAMo840EX7gwZCoog4JGxKJGJfzbBmJjIi5300UUP8saxo'
  # SHLVL: "3"
