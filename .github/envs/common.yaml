containerImage:
  pullPolicy: IfNotPresent
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-read-timeout: "840"
  path: /
  pathType: Prefix
  servicePort: app
containerPorts:
  http:
    port: 3000
secrets:
  api-service-secrets:
    as: envFrom
  strapi-rds-secrets:
    as: envFrom