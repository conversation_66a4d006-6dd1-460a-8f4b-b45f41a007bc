-- Initialize PostgreSQL database for DeFi Safety API
-- This script creates the schema for the internal database

-- Create the defisafety schema
CREATE SCHEMA IF NOT EXISTS defisafety;

-- Set the search path to include the defisafety schema
ALTER DATABASE defisafety SET search_path TO defisafety, public;

-- Grant permissions to the postgres user
GRANT ALL PRIVILEGES ON SCHEMA defisafety TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA defisafety TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA defisafety TO postgres;

-- Note: The actual tables will be created by TypeORM migrations when the API starts
