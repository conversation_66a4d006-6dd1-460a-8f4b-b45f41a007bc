-- Initialize PostgreSQL database for DeFi Safety API
-- This script creates the schema for the internal database

-- Create the defisafetyprod schema
CREATE SCHEMA IF NOT EXISTS defisafetyprod;

-- Set the search path to include the defisafetyprod schema
ALTER DATABASE defisafety SET search_path TO defisafetyprod, public;

-- Grant permissions to the postgres user
GRANT ALL PRIVILEGES ON SCHEMA defisafetyprod TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA defisafetyprod TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA defisafetyprod TO postgres;

-- Note: The actual tables will be created by TypeORM migrations when the API starts
