version: '3.8'

services:
  # PostgreSQL Database for internal app data
  postgres:
    image: postgres:15-alpine
    container_name: defisafety-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: defisafety
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5435:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - defisafety-network

  # MSSQL Database for contract-score data
  mssql:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: defisafety-mssql
    restart: unless-stopped
    environment:
      SA_PASSWORD: Secret1234
      ACCEPT_EULA: Y
      MSSQL_PID: Express
    ports:
      - '1435:1433'
    volumes:
      - mssql_data:/var/opt/mssql
      - ./init-mssql.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - defisafety-network

  # Main API Application
  api:
    build: .
    container_name: defisafety-api
    restart: unless-stopped
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=postgres
      - DATA_DB_HOST=mssql
    env_file:
      - .env
    depends_on:
      - postgres
      - mssql
    networks:
      - defisafety-network

volumes:
  postgres_data:
  mssql_data:

networks:
  defisafety-network:
    driver: bridge
