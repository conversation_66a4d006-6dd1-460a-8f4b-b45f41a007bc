# DeFi Safety API

## Description

Main backend for DeFi Safety Project.

Connected with MSSQL DB to retrieve contract-scores.
Connected with strapi admin panel to retrieve pqrs and chain-scores.
Cron task on the board to retrieve payment information from blockchains.
Tests are maintainable, please keep coverage > 60-70%.

Swagger document: http://localhost:3000/api/

Client swagger documents (accessible by api-key):

- http://localhost:3000/contract-scores-api/
- http://localhost:3000/pqrs-api/
- http://localhost:3000/chain-scores-api/

## Quick start

```bash
$ yarn install
$ cp .env.example .env
# fulfill .env by correct environment
$ docker-compose up
$ yarn run internalDb:migration:run
$ yarn run start:dev
```

## Running the app

```bash
# development
$ yarn run start
# watch mode
$ yarn run start:dev
# production mode
$ yarn run start:prod
```

## Migrations

```bash
# create new migration
$ yarn run typeorm -- -c internalDb migration:create -n User
# apply migrations
$ yarn run internalDb:migration:run
# rollback last applied migration
$ yarn run internalDb:migration:revert
```

## Test

```bash
# unit tests
$ yarn run test
# e2e tests
$ yarn run test:e2e
# test coverage
$ yarn run test:cov
```

## Envs

- dev --> https://api.dev.defisafety.com
- prod --> https://api.defisafety.com
