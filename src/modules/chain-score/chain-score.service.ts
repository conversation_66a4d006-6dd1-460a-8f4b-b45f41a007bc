import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import {
  ChainScoreFullResponseDto,
  ChainScoreRequestDto,
  ChainScoresRequestDto,
  ChainScoresResponsePagingDto,
} from '@modules/chain-score/dto/chain-score.dto';

@Injectable()
export class ChainScoreService {
  private host = process.env.STRAPI_HOST;
  private token = process.env.STRAPI_BEARER_TOKEN;
  private logger = new Logger(ChainScoreService.name);

  constructor(private readonly httpService: HttpService) {}

  async getChainScores(
    query: ChainScoresRequestDto,
    userId = '',
  ): Promise<ChainScoresResponsePagingDto> {
    const { order, orderBy, search, ...params } = query;

    this.logger.log(
      `Get chain scores: ${JSON.stringify(query)}, user: ${userId}`,
    );

    const res = await this.httpService
      .get(`${this.host}/chains`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          'x-app-user-id': userId,
          'x-app-user-access': 'true',
        },
        params: { ...params, sortBy: orderBy, sortOrder: order, name: search },
      })
      .toPromise();

    return {
      offset: Number(query.offset) || 0,
      limit: Number(query.limit) || 10,
      total: res.data.total,
      data: res.data.data,
    };
  }

  async getChainScore(
    { id }: ChainScoreRequestDto,
    userId = '',
  ): Promise<ChainScoreFullResponseDto> {
    this.logger.log(`Get chain score: ${id}, user: ${userId}`);

    const res = await this.httpService
      .get(`${this.host}/chains/${id}`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          'x-app-user-id': userId,
          'x-app-user-access': 'true',
        },
      })
      .toPromise();

    return res.data;
  }
}
