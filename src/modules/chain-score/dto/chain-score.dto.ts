import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { PagingRequestDto } from '@common/dto/paging-request.dto';
import { OrderDto } from '@common/dto/order.dto';
import { BaseDto } from '@common/dto/base.dto';
import { PagingResponseDto } from '@common/dto/paging-response.dto';

export enum ChainScoresRequestOrderByDto {
  name = 'name',
  date = 'date',
  version = 'version',
  overallScore = 'overallScore',
}

export class ChainScoresRequestDto extends PagingRequestDto {
  @ApiPropertyOptional({
    description:
      'Search by chain name substring (ex: "thereu"). Registry independent: "Polygon" and "poLYGoN" will provide the same result',
    default: undefined,
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(ChainScoresRequestOrderByDto)
  orderBy?: ChainScoresRequestOrderByDto;

  @ApiPropertyOptional({
    enum: OrderDto,
    description: '"DESC" by default (if "order" is provided).',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(OrderDto)
  order?: OrderDto;
}

export class ChainScoreAuditDto {
  @ApiProperty()
  sectionName: string;

  @ApiProperty({ type: 'integer' })
  totalScore: number;
}

export class ChainScoreResponseDto extends BaseDto {
  @ApiProperty()
  id: number;
  @ApiProperty()
  name: string;
  @ApiProperty({ format: 'url' })
  icon: string;
  @ApiProperty({ format: 'date' })
  date: Date;
  @ApiProperty()
  version: number;
  @ApiProperty()
  overallScore: number;
  @ApiProperty({ format: 'url' })
  url: string;
  @ApiProperty({ type: [ChainScoreAuditDto] })
  audit: ChainScoreAuditDto[];
}

export class ChainScoresResponsePagingDto extends PagingResponseDto<ChainScoreResponseDto> {
  @ApiProperty({ type: [ChainScoreResponseDto] })
  data: ChainScoreResponseDto[];
}

export class ChainScoreRequestDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  id: number;
}

export class ChainScoreAuditQuestionGuidance {
  @ApiProperty()
  score: number;
  @ApiProperty()
  text: string;
}

export class ChainScoreAuditQuestion {
  @ApiProperty({ type: 'uuid' })
  id: string;
  @ApiProperty()
  question: string;
  @ApiProperty()
  justification: string;
  @ApiProperty({ type: [ChainScoreAuditQuestionGuidance] })
  guidance: ChainScoreAuditQuestionGuidance[];
  @ApiProperty()
  answer: number;
}

export class ChainScoreFullAuditDto extends ChainScoreAuditDto {
  @ApiProperty()
  sectionDescription: string;

  @ApiProperty({ type: [ChainScoreAuditQuestion] })
  questions: ChainScoreAuditQuestion[];
}

export class ChainScoreFullResponseDto extends ChainScoreResponseDto {
  @ApiProperty({ type: [ChainScoreFullAuditDto] })
  audit: ChainScoreFullAuditDto[];
}
