import { Test, TestingModule } from '@nestjs/testing';
import { ChainScoreController } from './chain-score.controller';
import { HttpModule } from '@nestjs/axios';
import { ChainScoreService } from '@modules/chain-score/chain-score.service';

describe('ChainScoreController', () => {
  let controller: ChainScoreController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChainScoreController],
      imports: [HttpModule],
      providers: [ChainScoreService],
    }).compile();

    controller = module.get<ChainScoreController>(ChainScoreController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
