import {
  Controller,
  Get,
  Param,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_FORBIDDEN,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { HttpService } from '@nestjs/axios';
import {
  ChainScoreFullResponseDto,
  ChainScoreRequestDto,
  ChainScoresRequestDto,
  ChainScoresResponsePagingDto,
} from '@modules/chain-score/dto/chain-score.dto';
import { ApiRole } from '@common/decorators/role.decorator';
import { API_PERMISSIONS_LIST } from '@common/utils/permissions';
import { ChainScoreService } from '@modules/chain-score/chain-score.service';
import { AccessStrategyDto } from '@modules/user/dto/strategy.dto';
import { JwtAuthApiKeyGuard } from '../user/jwt-api-key.guard';

@ApiTags('Chain Scores')
// Limited/full access check moved to strapi
// @Role(PLAN_PERMISSIONS_LIST.CHAIN_SCORES)
@ApiRole(API_PERMISSIONS_LIST.CHAIN_SCORES)
@ApiBearerAuth('api-key')
@ApiSecurity({ 'access-token': [], 'x-application-key': [] })
@UseGuards(new JwtAuthApiKeyGuard({ optional: true }))
@Controller('chain-scores')
export class ChainScoreController {
  constructor(
    private httpService: HttpService,
    private readonly chainScoreService: ChainScoreService,
  ) {}

  @Get('')
  @ApiOperation({ summary: 'Get chain scores.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getChainScores(
    @Request() { user }: AccessStrategyDto,
    @Query() query: ChainScoresRequestDto,
  ): Promise<ChainScoresResponsePagingDto> {
    return this.chainScoreService.getChainScores(query, user?.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get single chain score by id.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  async getChainScore(
    @Request() { user }: AccessStrategyDto,
    @Param() { id }: ChainScoreRequestDto,
  ): Promise<ChainScoreFullResponseDto> {
    return this.chainScoreService.getChainScore({ id }, user?.id);
  }
}
