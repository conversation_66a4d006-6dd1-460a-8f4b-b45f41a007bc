import { DynamicModule, Global, Module, ValueProvider } from '@nestjs/common';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { PAYMENT_GATEWAY_TOKEN } from '@modules/payment-gateway/tokens';
import { StripeConfig } from '@modules/payment-gateway/dto/payment-gateway.dto';

@Global()
@Module({})
export class PaymentGatewayModule {
  public static forRoot(options: StripeConfig): DynamicModule {
    const MailerOptionsProvider: ValueProvider<StripeConfig> = {
      provide: PAYMENT_GATEWAY_TOKEN,
      useValue: options,
    };

    return {
      module: PaymentGatewayModule,
      providers: [MailerOptionsProvider, PaymentGatewayService],
      exports: [PaymentGatewayService],
    };
  }
}
