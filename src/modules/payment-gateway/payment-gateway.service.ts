import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PAYMENT_GATEWAY_TOKEN } from '@modules/payment-gateway/tokens';
import { Stripe } from 'stripe';
import {
  CancelSubscription,
  CreateSubscriptionSession,
  CreateSubscriptionSessionResponseDto,
  StripeConfig,
} from '@modules/payment-gateway/dto/payment-gateway.dto';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
} from '@config/error-messages';

@Injectable()
export class PaymentGatewayService {
  private readonly logger = new Logger(PaymentGatewayService.name);
  stripe: Stripe;

  constructor(
    @Inject(PAYMENT_GATEWAY_TOKEN)
    private readonly paymentGatewayOptions: StripeConfig,
  ) {
    this.stripe = new Stripe(
      paymentGatewayOptions.apiKey,
      paymentGatewayOptions.config,
    );
  }

  async createSubscriptionSession({
    userId,
    userEmail,
    payerId,
    priceId,
  }: CreateSubscriptionSession): Promise<CreateSubscriptionSessionResponseDto> {
    let session: Stripe.Response<Stripe.Checkout.Session>;

    try {
      session = await this.stripe.checkout.sessions.create({
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        customer: payerId,
        customer_email: payerId ? undefined : userEmail,
        client_reference_id: userId,
        success_url: `${process.env.FRONT_END_HOST}/app/payment-success`,
        cancel_url: `${process.env.FRONT_END_HOST}/app/payment-failure`,
      });
    } catch (err) {
      this.logger.error(err);
      throw new InternalServerErrorException(ERROR_HTTP_INTERNAL_SERVER_ERROR);
    }

    return { redirect: session.url };
  }

  async checkWebhookSignature({ body, signature }) {
    const { STRIPE_WEBHOOK_SECRET } = process.env;

    let event: Stripe.Event;

    try {
      event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        STRIPE_WEBHOOK_SECRET,
      );
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    const { data, type } = event;

    return { data, type };
  }

  getProductById({ productId }): Promise<Stripe.Response<Stripe.Product>> {
    if (!productId) {
      this.logger.error(`Not found productId: ${productId}`);
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    return this.stripe.products.retrieve(productId);
  }

  getProducts(): Stripe.ApiListPromise<Stripe.Product> {
    return this.stripe.products.list({ active: true, limit: 100 });
  }

  getPrices(): Stripe.ApiListPromise<Stripe.Price> {
    return this.stripe.prices.list({ active: true, limit: 100 });
  }

  getSubscriptions({ customerId }) {
    return this.stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
    });
  }

  async getCurrentSubscription({ customerId }) {
    const { data: subscriptions } = await this.getSubscriptions({ customerId });

    if (subscriptions?.length) {
      subscriptions.sort((a, b) => b.created - a.created);

      return subscriptions[0];
    }

    return null;
  }

  getPlanDetails({ priceId }) {
    return this.stripe.prices.retrieve(priceId, { expand: ['product'] });
  }

  cancelSubscription({
    id,
  }: CancelSubscription): Promise<Stripe.Response<Stripe.Subscription>> {
    return this.stripe.subscriptions.update(id, { cancel_at_period_end: true });
  }
}
