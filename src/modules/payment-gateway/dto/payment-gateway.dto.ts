import { Stripe } from 'stripe';
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUrl } from 'class-validator';

export type StripeConfig = {
  apiKey: string;
  config?: Stripe.StripeConfig;
};

export interface CreateSubscriptionSessionParams {
  priceId: string;
}

export interface CreateSubscriptionSession
  extends CreateSubscriptionSessionParams {
  priceId: string;
  userId: string;
  userEmail?: string;
  payerId?: string;
}

export class CreateSubscriptionSessionParamsDto
  implements CreateSubscriptionSessionParams
{
  @ApiProperty()
  @IsString()
  priceId: string;
}

export class CreateSubscriptionSessionResponseDto {
  @ApiProperty({
    type: 'url',
    description: 'Payment page link.',
  })
  @IsString()
  @IsUrl()
  redirect: string;
}

export interface WebhookResponseDto {
  success: boolean;
}

export interface CancelSubscription {
  id: string;
}

export class CancelSubscriptionResponseDto {
  @ApiProperty()
  success: boolean;
}
