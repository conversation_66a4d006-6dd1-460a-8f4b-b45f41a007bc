import { Controller, Get } from '@nestjs/common';
import {
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import { ScoreCountResponseDto } from '@modules/contract-score/dto/score.dto';
import { ERROR_HTTP_INTERNAL_SERVER_ERROR } from '@config/error-messages';

@ApiTags('Contract Scores Count')
@Controller('contract-scores/count')
export class ContractScoreCountController {
  constructor(private readonly contractScoreService: ContractScoreService) {}
  @Get('')
  @ApiOperation({ summary: 'Get amount of contract scores.' })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getCount(): Promise<ScoreCountResponseDto> {
    return this.contractScoreService.getCount();
  }
}
