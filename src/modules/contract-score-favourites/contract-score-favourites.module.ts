import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';
import { DATA_DB_CONNECTION, INTERNAL_DB_CONNECTION } from '@config/tokens';
import { FavouriteContractScore } from '@root/internal-db/models/FavouriteContractScore';
import { FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN } from '@modules/contract-score-favourites/tokens';
import { FavouriteContractScoreRepository } from '@modules/contract-score-favourites/favourite-contract-score.repository';
import { ContractScoreFavouritesService } from '@modules/contract-score-favourites/contract-score-favourites.service';
import { ContractScoreModule } from '@modules/contract-score/contract-score.module';
import { ContractScoreFavouritesController } from '@modules/contract-score-favourites/contract-score-favourites.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([SearchableEntity], DATA_DB_CONNECTION),
    TypeOrmModule.forFeature([FavouriteContractScore], INTERNAL_DB_CONNECTION),
    ContractScoreModule,
  ],
  controllers: [ContractScoreFavouritesController],
  providers: [
    ContractScoreFavouritesService,
    {
      provide: FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN,
      useClass: FavouriteContractScoreRepository,
    },
  ],
  exports: [FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN],
})
export class ContractScoreFavouritesModule {}
