import { ApiProperty } from '@nestjs/swagger';
import { IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export interface FavouritesRequestDto {
  userId: string;
}

export interface FavouritesPaginatedRequest extends FavouritesRequestDto {
  offset: number;
  limit: number;
}

export class CreateFavourite {
  userId: string;
  contractScoreId: number;
}

export class DeleteFavourite {
  userId: string;
  contractScoreId: number;
}

export class CreateFavouriteRequestDto {
  @ApiProperty()
  @Type(() => Number)
  @IsInt()
  contractScoreId: number;
}

export class DeleteFavouriteRequestDto {
  @ApiProperty()
  @Type(() => Number)
  @IsInt()
  contractScoreId: number;
}

export class FavouriteDto {
  @ApiProperty()
  id: number;

  @ApiProperty({
    nullable: false,
    type: 'uuid',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    nullable: false,
  })
  contractScoreId: number;

  @ApiProperty({
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    type: Date,
  })
  updatedAt: Date;
}

export class DeleteFavouriteResponseDto {
  @ApiProperty({
    type: Boolean,
  })
  success: boolean;
}
