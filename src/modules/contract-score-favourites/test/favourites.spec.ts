import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_NOT_FOUND,
} from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { FavouriteContractScoreRepository } from '@modules/contract-score-favourites/favourite-contract-score.repository';
import { FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN } from '@modules/contract-score-favourites/tokens';
import { UserService } from '@modules/user/user.service';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';
import { Repository } from 'typeorm';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DATA_DB_CONNECTION } from '@config/tokens';
import { User } from '@root/internal-db/models/User';

describe('Favourite contract scores', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let userRepository: UserRepository;
  let favouriteContractScoreRepository: FavouriteContractScoreRepository;
  let searchableRepository: Repository<SearchableEntity>;

  let user1: User;
  let user2: User;
  let user1Tokens: SignInResponseDto;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userService = module.get<UserService>(UserService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    favouriteContractScoreRepository = module.get(
      FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN,
    );
    searchableRepository = module.get<Repository<SearchableEntity>>(
      getRepositoryToken(SearchableEntity, DATA_DB_CONNECTION),
    );

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    user1Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });

    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
  });

  it('should retrieve list of favourites contract scores', async () => {
    const searchableRepositoryMock = jest
      .spyOn(searchableRepository, 'createQueryBuilder')
      .mockImplementation(
        jest.fn().mockReturnValue({
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getRawMany: jest.fn().mockResolvedValue([]),
        }),
      );

    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 10,
    });
    await favouriteContractScoreRepository.create({
      userId: user2.id,
      contractScoreId: 15,
    });
    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 17,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores/favourites')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(body.total).toBe(2);

    searchableRepositoryMock.mockRestore();
    await favouriteContractScoreRepository.deleteOne({
      userId: user1.id,
      contractScoreId: 10,
    });
    await favouriteContractScoreRepository.deleteOne({
      userId: user2.id,
      contractScoreId: 15,
    });
    await favouriteContractScoreRepository.deleteOne({
      userId: user1.id,
      contractScoreId: 17,
    });
  });

  it('should retrieve empty list of favourites contract scores', async () => {
    const searchableRepositoryMock = jest
      .spyOn(searchableRepository, 'createQueryBuilder')
      .mockImplementation(
        jest.fn().mockReturnValue({
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getRawMany: jest.fn().mockResolvedValue([]),
        }),
      );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores/favourites')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(body.total).toBe(0);
    expect(body.data.length).toBe(0);

    searchableRepositoryMock.mockRestore();
  });

  it('should create new favourite contract score', async () => {
    const searchableRepositoryMock = jest
      .spyOn(searchableRepository, 'findOne')
      .mockImplementation(({ id }) => {
        expect(id).toBe(20);

        return Promise.resolve({} as SearchableEntity);
      });
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/contract-scores/favourites')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({ contractScoreId: 20 });

    expect(status).toBe(200);
    expect(body.contractScoreId).toBe(20);
    expect(body.userId).toBe(user1.id);
    expect(searchableRepositoryMock.mock.calls.length).toBe(1);

    searchableRepositoryMock.mockRestore();
  });

  it('should throw an error because target favourite contract score for this user already defined', async () => {
    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 30,
    });

    const searchableRepositoryMock = jest
      .spyOn(searchableRepository, 'findOne')
      .mockImplementation(() => {
        return Promise.resolve({} as SearchableEntity);
      });

    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/contract-scores/favourites')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({ contractScoreId: 30 });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    searchableRepositoryMock.mockRestore();
  });

  it('should throw an error because contract-score not exists in data DB', async () => {
    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 40,
    });

    const searchableRepositoryMock = jest
      .spyOn(searchableRepository, 'findOne')
      .mockImplementation(() => {
        return Promise.resolve(undefined);
      });
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/contract-scores/favourites')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({ contractScoreId: 30 });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    searchableRepositoryMock.mockRestore();
  });

  it('should delete correct favourite contract score', async () => {
    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 50,
    });
    await favouriteContractScoreRepository.create({
      userId: user2.id,
      contractScoreId: 50,
    });
    await favouriteContractScoreRepository.create({
      userId: user1.id,
      contractScoreId: 51,
    });

    const deleted = await supertest
      .agent(app.getHttpServer())
      .delete('/contract-scores/favourites/50')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({ contractScoreId: 50 });

    expect(deleted.status).toBe(200);
    expect(deleted.body.success).toBe(true);

    const user1score50 = await favouriteContractScoreRepository.findOne({
      userId: user1.id,
      contractScoreId: 50,
    });
    const user2score50 = await favouriteContractScoreRepository.findOne({
      userId: user2.id,
      contractScoreId: 50,
    });
    const user1score51 = await favouriteContractScoreRepository.findOne({
      userId: user1.id,
      contractScoreId: 51,
    });

    expect(user1score50).toBeUndefined();
    expect(user2score50).toBeDefined();
    expect(user1score51).toBeDefined();

    const duplicate = await supertest
      .agent(app.getHttpServer())
      .delete('/contract-scores/favourites/50')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({ contractScoreId: 50 });

    expect(duplicate.status).toBe(404);
    expect(duplicate.body.message).toBe(ERROR_HTTP_NOT_FOUND);
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
