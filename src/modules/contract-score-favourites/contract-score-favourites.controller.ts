import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { GetScorePagingDto } from '@modules/contract-score/dto/contract-score-paging.dto';
import { AccessStrategyDto } from '@modules/user/dto/strategy.dto';
import { PagingRequestDto } from '@common/dto/paging-request.dto';
import {
  CreateFavouriteRequestDto,
  DeleteFavouriteRequestDto,
  DeleteFavouriteResponseDto,
  FavouriteDto,
} from '@modules/contract-score-favourites/dto/favourites.dto';
import { JwtAuthGuard } from '@modules/user/jwt.guard';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { ContractScoreFavouritesService } from '@modules/contract-score-favourites/contract-score-favourites.service';

@ApiTags('Contract Scores Favourites')
@Controller('contract-scores/favourites')
export class ContractScoreFavouritesController {
  constructor(
    private readonly contractScoreFavouritesService: ContractScoreFavouritesService,
  ) {}

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get contract-scores, market as favourites.',
  })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  favourites(
    @Request() req: AccessStrategyDto,
    @Query() { limit, offset }: PagingRequestDto,
  ): Promise<GetScorePagingDto> {
    return this.contractScoreFavouritesService.getFavourites({
      userId: req.user.id,
      limit,
      offset,
    });
  }

  @Post('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Create new favourite contract-score.',
  })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  createFavourite(
    @Request() req: AccessStrategyDto,
    @Body() body: CreateFavouriteRequestDto,
  ): Promise<FavouriteDto> {
    return this.contractScoreFavouritesService.createFavourite({
      userId: req.user.id,
      contractScoreId: body.contractScoreId,
    });
  }

  @Delete('/:contractScoreId')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Remove favourite contract-score.',
  })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  deleteFavourite(
    @Request() req: AccessStrategyDto,
    @Param() { contractScoreId }: DeleteFavouriteRequestDto,
  ): Promise<DeleteFavouriteResponseDto> {
    return this.contractScoreFavouritesService.deleteFavourite({
      userId: req.user.id,
      contractScoreId,
    });
  }
}
