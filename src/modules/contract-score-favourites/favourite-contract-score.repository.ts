import { Injectable } from '@nestjs/common';
import { DeleteResult, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { FavouriteContractScore } from '@root/internal-db/models/FavouriteContractScore';
import {
  CreateFavourite,
  FavouritesPaginatedRequest,
} from '@modules/contract-score-favourites/dto/favourites.dto';

@Injectable()
export class FavouriteContractScoreRepository {
  constructor(
    @InjectRepository(FavouriteContractScore, INTERNAL_DB_CONNECTION)
    private readonly favouriteContractScoreRepository: Repository<FavouriteContractScore>,
  ) {}

  findOne({
    userId,
    contractScoreId,
  }): Promise<FavouriteContractScore | undefined> {
    return this.favouriteContractScoreRepository.findOne({
      userId,
      contractScoreId,
    });
  }

  findByUser({
    userId,
    limit,
    offset,
  }: FavouritesPaginatedRequest): Promise<[FavouriteContractScore[], number]> {
    return this.favouriteContractScoreRepository.findAndCount({
      where: { userId },
      skip: offset,
      take: limit,
    });
  }

  create({
    userId,
    contractScoreId,
  }: CreateFavourite): Promise<FavouriteContractScore> {
    const favouriteContractScore = this.favouriteContractScoreRepository.create(
      { userId, contractScoreId },
    );

    return this.favouriteContractScoreRepository.save(favouriteContractScore);
  }

  deleteOne({ userId, contractScoreId }): Promise<DeleteResult> {
    return this.favouriteContractScoreRepository.delete({
      userId,
      contractScoreId,
    });
  }
}
