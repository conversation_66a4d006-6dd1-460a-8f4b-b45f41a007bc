import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, MoreThanOrEqual, Not, Repository } from 'typeorm';
import { GetScorePagingDto } from '@modules/contract-score/dto/contract-score-paging.dto';
import { DATA_DB_CONNECTION } from '@config/tokens';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';
import {
  CreateFavourite,
  DeleteFavourite,
  DeleteFavouriteResponseDto,
  FavouriteDto,
  FavouritesPaginatedRequest,
} from '@modules/contract-score-favourites/dto/favourites.dto';
import { FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN } from '@modules/contract-score-favourites/tokens';
import { FavouriteContractScoreRepository } from '@modules/contract-score-favourites/favourite-contract-score.repository';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_NOT_FOUND,
} from '@config/error-messages';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';

@Injectable()
export class ContractScoreFavouritesService {
  constructor(
    private readonly contractScoreService: ContractScoreService,
    @InjectRepository(SearchableEntity, DATA_DB_CONNECTION)
    private searchableRepository: Repository<SearchableEntity>,
    @Inject(FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN)
    private readonly favouriteContractScoreRepository: FavouriteContractScoreRepository,
  ) {}

  async getFavourites({
    userId,
    limit,
    offset,
  }: FavouritesPaginatedRequest): Promise<GetScorePagingDto> {
    const [userFavourites, total] =
      await this.favouriteContractScoreRepository.findByUser({
        userId,
        limit,
        offset,
      });
    const contractScoreIds: number[] = userFavourites.map(
      (item) => item.contractScoreId,
    );

    const contractScores = contractScoreIds.length
      ? await this.searchableRepository
          .createQueryBuilder()
          .select(this.contractScoreService.select)
          .where('"id" IN (:...contractScoreIds)', { contractScoreIds })
          .getRawMany()
      : [];

    return {
      offset: Number(offset),
      limit: Number(limit),
      total,
      data: contractScores,
    };
  }

  async createFavourite(params: CreateFavourite): Promise<FavouriteDto> {
    const existingRelation =
      await this.favouriteContractScoreRepository.findOne(params);
    const isFavouriteExist = await this.searchableRepository.findOne({
      id: params.contractScoreId,
      contract_score: Not(IsNull()),
      defisafety_score: MoreThanOrEqual(0),
      audit_score: MoreThanOrEqual(0),
    });

    if (existingRelation || !isFavouriteExist) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    return this.favouriteContractScoreRepository.create(params);
  }

  async deleteFavourite(
    params: DeleteFavourite,
  ): Promise<DeleteFavouriteResponseDto> {
    const res = await this.favouriteContractScoreRepository.deleteOne(params);

    if (!res.affected) {
      throw new NotFoundException(ERROR_HTTP_NOT_FOUND);
    }

    return { success: true };
  }
}
