import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from './email.service';
import { NODEMAILER_OPTIONS_TOKEN } from '@modules/email/tokens';
import {
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
} from '@root/setup/e2e-test.setup';
import {
  ConfirmationEmailParamsDto,
  ForgotPasswordEmailParamsDto,
} from '@modules/email/dto/email.dto';

describe('EmailService', () => {
  let service: EmailService;
  const mailTo = TEST_DEFAULT_EMAIL_1;
  const mailFrom = TEST_DEFAULT_EMAIL_2;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: NODEMAILER_OPTIONS_TOKEN,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    service.transporter.options = {
      auth: { user: mailFrom },
    };
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should send an confirmation email and include params', async () => {
    const mailSubjectConfirmation = 'Email confirmation';
    const mailParamsConfirmation: ConfirmationEmailParamsDto = {
      host: '#host#',
      name: '#name#',
      token: '#token#',
    };

    const sendEmailMock = jest
      .spyOn(service.transporter, 'sendMail')
      .mockImplementation(({ from, to, subject, html }) => {
        expect(from).toContain('DeFi Safety');
        expect(from).toContain(`<${mailFrom}>`);
        expect(to).toBe(mailTo);
        expect(subject).toBe(mailSubjectConfirmation);
        expect(html).toContain(mailParamsConfirmation.host);
        expect(html).toContain(mailParamsConfirmation.name);
        expect(html).toContain(mailParamsConfirmation.token);

        return Promise.resolve({});
      });

    await service.sendConfirmationEmail({
      params: mailParamsConfirmation,
      to: mailTo,
    });
    expect(sendEmailMock.mock.calls.length).toBe(1);

    sendEmailMock.mockRestore();
  });

  it('should send an restore password email and include params', async () => {
    const mailSubjectForgotPassword = 'Password restoring';
    const mailParamsForgotPassword: ForgotPasswordEmailParamsDto = {
      host: '#host#',
      name: '#name#',
      token: '#token#',
    };

    const sendEmailMock = jest
      .spyOn(service.transporter, 'sendMail')
      .mockImplementation(({ from, to, subject, html }) => {
        expect(from).toContain('DeFi Safety');
        expect(from).toContain(`<${mailFrom}>`);
        expect(to).toBe(mailTo);
        expect(subject).toBe(mailSubjectForgotPassword);
        expect(html).toContain(mailParamsForgotPassword.host);
        expect(html).toContain(mailParamsForgotPassword.name);
        expect(html).toContain(mailParamsForgotPassword.token);

        return Promise.resolve({});
      });

    await service.sendForgotPasswordEmail({
      params: mailParamsForgotPassword,
      to: mailTo,
    });
    expect(sendEmailMock.mock.calls.length).toBe(1);

    sendEmailMock.mockRestore();
  });
});
