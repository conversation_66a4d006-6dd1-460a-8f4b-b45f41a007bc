import { DynamicModule, Global, Module, ValueProvider } from '@nestjs/common';
import { EmailService } from './email.service';
import { NODEMAILER_OPTIONS_TOKEN } from '@modules/email/tokens';
import { NodemailerOptions } from '@modules/email/dto/nodemailer-options.dto';

@Global()
@Module({})
export class EmailModule {
  public static forRoot(options: NodemailerOptions): DynamicModule {
    const MailerOptionsProvider: ValueProvider<NodemailerOptions> = {
      provide: NODEMAILER_OPTIONS_TOKEN,
      useValue: options,
    };

    return {
      module: EmailModule,
      providers: [MailerOptionsProvider, EmailService],
      exports: [EmailService],
    };
  }
}
