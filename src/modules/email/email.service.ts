import { Inject, Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { renderFile } from 'ejs';
import {
  ConfirmationEmailParamsDto,
  ConfirmationSubscriptionParamsDto,
  EmailParamsDto,
  ForgotPasswordEmailParamsDto,
} from '@modules/email/dto/email.dto';
import { NodemailerOptions } from '@modules/email/dto/nodemailer-options.dto';
import { NODEMAILER_OPTIONS_TOKEN } from '@modules/email/tokens';

enum TemplateList {
  'confirm-subscription' = 'confirm-subscription',
  'confirm-email' = 'confirm-email',
  'forgot-password' = 'forgot-password',
}

@Injectable()
export class EmailService {
  transporter;

  constructor(
    @Inject(NODEMAILER_OPTIONS_TOKEN)
    private readonly nodemailerOptions: NodemailerOptions,
  ) {
    this.transporter = nodemailer.createTransport(nodemailerOptions);
  }

  private async sendEmail(params, to, subject, template: TemplateList) {
    const html = await renderFile(
      __dirname + `/templates/${template}.template.html`,
      params,
    );

    return this.transporter.sendMail({
      from: `"DeFi Safety" <${this.transporter.options.auth.user}>`,
      to,
      subject,
      html,
    });
  }

  sendConfirmationSubscription({
    params,
    to,
  }: EmailParamsDto<ConfirmationSubscriptionParamsDto>) {
    return this.sendEmail(
      params,
      to,
      'Successful Subscription Confirmation: Welcome to DefiSafety',
      TemplateList['confirm-subscription'],
    );
  }

  sendConfirmationEmail({
    params,
    to,
  }: EmailParamsDto<ConfirmationEmailParamsDto>) {
    return this.sendEmail(
      params,
      to,
      'Email confirmation',
      TemplateList['confirm-email'],
    );
  }

  sendForgotPasswordEmail({
    params,
    to,
  }: EmailParamsDto<ForgotPasswordEmailParamsDto>) {
    return this.sendEmail(
      params,
      to,
      'Password restoring',
      TemplateList['forgot-password'],
    );
  }
}
