import { PagingRequestDto } from '@common/dto/paging-request.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';
import { UserResponseDto } from '@modules/user/dto/user.dto';
import { SubscriptionDtoResponse } from '@modules/payment/dto/subscription.dto';
import {
  ERROR_VALIDATION_EMAIL,
  ERROR_VALIDATION_PASSWORD,
} from '@config/error-messages';
import { OrderDto } from '@common/dto/order.dto';

export const UsersAdminRequestOrderByDto = {
  email: 'email',
  emailConfirmed: 'emailConfirmed',
  publicAddress: 'publicAddress',
  permissions: 'permissions',
  permissionExpiration: 'permissionExpiration',
  apiPermissions: 'apiPermissions',
  apiPermissionToken: 'apiPermissionToken',
  apiPermissionExpiration: 'apiPermissionExpiration',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
};

export class UsersAdminRequestDto extends PagingRequestDto {
  @ApiPropertyOptional({
    description: 'Search by user email or wallet address',
    default: undefined,
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: Object.keys(UsersAdminRequestOrderByDto),
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(Object.keys(UsersAdminRequestOrderByDto))
  orderBy: string;

  @ApiPropertyOptional({
    enum: OrderDto,
    description: '"DESC" by default (if "order" is provided).',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(OrderDto)
  order: OrderDto;
}

export class UserAdminRequestDto {
  @ApiProperty()
  @Type(() => String)
  @IsString()
  id: string;
}

export class CreateUserAdminRequestDto {
  @ApiPropertyOptional({
    example: '<EMAIL>',
  })
  @IsString()
  @IsEmail({}, { message: ERROR_VALIDATION_EMAIL })
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    description:
      'Requirements: minimum length - 8 symbols, must contain at least one digit, must contain at least one special character (from the list: !@#$%^&*)',
    example: 'password1#',
  })
  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[\d])(?=.*[!@#$%^&*])[\w!@#$%^&*]{8,}$/, {
    message: ERROR_VALIDATION_PASSWORD,
  })
  @IsOptional()
  password?: string;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  publicAddress?: string;

  @ApiPropertyOptional({
    default: 0,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  permissions?: number;

  @ApiPropertyOptional()
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  permissionExpirationTimestamp?: number;

  @ApiPropertyOptional({
    default: 0,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  apiPermissions?: number;

  @ApiPropertyOptional()
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  apiPermissionExpirationTimestamp?: number;
}

export class UpdateUserAdminRequestDto {
  @ApiPropertyOptional({
    example: '<EMAIL>',
  })
  @IsString()
  @IsEmail({}, { message: ERROR_VALIDATION_EMAIL })
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  publicAddress?: string;

  @ApiPropertyOptional({
    default: 0,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  permissions?: number;

  @ApiPropertyOptional()
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  permissionExpirationTimestamp?: number;

  @ApiPropertyOptional({
    default: 0,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  apiPermissions?: number;

  @ApiPropertyOptional()
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  apiPermissionExpirationTimestamp?: number;
}

export class UserAdminResponseDto extends UserResponseDto {
  subscription: SubscriptionDtoResponse;
}
