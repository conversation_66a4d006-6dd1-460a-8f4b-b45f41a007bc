import { Module } from '@nestjs/common';
import { AdminApiKeyStrategy } from '@modules/admin/admin-api-key.strategy';
import { AdminService } from '@modules/admin/admin.service';
import { AdminController } from '@modules/admin/admin.controller';
import { PaymentModule } from '@modules/payment/payment.module';
import { UserModule } from '@modules/user/user.module';

@Module({
  imports: [UserModule, PaymentModule],
  providers: [AdminApiKeyStrategy, AdminService],
  controllers: [AdminController],
})
export class AdminModule {}
