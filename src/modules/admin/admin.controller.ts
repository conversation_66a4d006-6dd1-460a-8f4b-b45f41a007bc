import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { AdminService } from '@modules/admin/admin.service';
import { AdminApiKeyGuard } from '@modules/admin/admin-api-key.guard';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_FORBIDDEN,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
} from '@config/error-messages';
import {
  CreateUserAdminRequestDto,
  UpdateUserAdminRequestDto,
  UserAdminRequestDto,
  UserAdminResponseDto,
  UsersAdminRequestDto,
} from '@modules/admin/dto/user.dto';
import {
  UpdateUserResponse,
  UsersResponsePagingDto,
} from '@modules/user/dto/user.dto';

@ApiTags('Admin')
@ApiBearerAuth('api-key')
@UseGuards(AdminApiKeyGuard)
@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('/users')
  @ApiOperation({ summary: 'Get Users.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getUsers(
    @Query() query: UsersAdminRequestDto,
  ): Promise<UsersResponsePagingDto> {
    return this.adminService.getUsers(query);
  }

  @Post('/users')
  @ApiOperation({ summary: 'Create User.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  createUser(@Body() body: CreateUserAdminRequestDto): Promise<string> {
    return this.adminService.createUser(body);
  }

  @Get('/users/:id')
  @ApiOperation({ summary: 'Get User by ID.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getUserById(
    @Param() { id }: UserAdminRequestDto,
  ): Promise<UserAdminResponseDto> {
    return this.adminService.getUserById(id);
  }

  @Put('/users/:id')
  @ApiOperation({ summary: 'Update User by ID.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  updateUser(
    @Param() { id }: UserAdminRequestDto,
    @Body() body: UpdateUserAdminRequestDto,
  ): Promise<UpdateUserResponse> {
    return this.adminService.updateUser(id, body);
  }
}
