import { Injectable, Logger } from '@nestjs/common';
import { UserService } from '@modules/user/user.service';
import {
  CreateUserAdminRequestDto,
  UserAdminResponseDto,
  UsersAdminRequestDto,
} from '@modules/admin/dto/user.dto';
import {
  UpdateUserResponse,
  UsersResponsePagingDto,
} from '@modules/user/dto/user.dto';
import { PaymentService } from '@modules/payment/payment.service';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    private readonly userService: UserService,
    private readonly paymentService: PaymentService,
  ) {}

  async getUsers({
    search,
    orderBy,
    order,
    offset,
    limit,
  }: UsersAdminRequestDto): Promise<UsersResponsePagingDto> {
    return this.userService.getUsers({
      search,
      orderBy,
      order,
      offset,
      limit,
    });
  }

  async getUserById(id: string): Promise<UserAdminResponseDto> {
    const user = await this.userService.getUserById({ id });
    const subscription = await this.paymentService.getSubscription({
      userId: id,
    });

    return {
      ...user,
      subscription,
    };
  }

  async createUser({
    email,
    password,
    publicAddress,
    permissions,
    permissionExpirationTimestamp,
    apiPermissions,
    apiPermissionExpirationTimestamp,
  }: CreateUserAdminRequestDto): Promise<string> {
    return this.userService.signUpByAdmin({
      email,
      password,
      publicAddress,
      permissions,
      permissionExpiration: permissionExpirationTimestamp
        ? new Date(permissionExpirationTimestamp)
        : null,
      apiPermissions,
      apiPermissionExpiration: apiPermissionExpirationTimestamp
        ? new Date(apiPermissionExpirationTimestamp)
        : null,
    });
  }

  async updateUser(
    id: string,
    {
      email,
      publicAddress,
      permissions,
      permissionExpirationTimestamp,
      apiPermissions,
      apiPermissionExpirationTimestamp,
    }: CreateUserAdminRequestDto,
  ): Promise<UpdateUserResponse> {
    return this.userService.updateUserByAdmin({
      id,
      email,
      publicAddress,
      permissions,
      permissionExpiration: permissionExpirationTimestamp
        ? new Date(permissionExpirationTimestamp)
        : null,
      apiPermissions,
      apiPermissionExpiration: apiPermissionExpirationTimestamp
        ? new Date(apiPermissionExpirationTimestamp)
        : null,
    });
  }
}
