import { Strategy } from '@modules/user/api-key.strategy';
import { PassportStrategy } from '@nestjs/passport';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { ERROR_HTTP_FORBIDDEN } from '@config/error-messages';
import { ADMIN_API_KEY_STRATEGY } from '@modules/user/tokens';
import { ApiKeyStrategyValidateDto } from '@modules/user/dto/strategy.dto';

@Injectable()
export class AdminApiKeyStrategy extends PassportStrategy(
  Strategy,
  ADMIN_API_KEY_STRATEGY,
) {
  private adminApiKey = process.env.ADMIN_API_KEY;

  constructor() {
    super('X-Api-Key', (apiKey, done) => this.validate(apiKey, done));
  }

  async validate(
    apiKey: string,
    done: (
      error: Error,
      user?: Record<string, unknown>,
    ) => ApiKeyStrategyValidateDto,
  ) {
    if (apiKey !== this.adminApiKey) {
      return done(new ForbiddenException(ERROR_HTTP_FORBIDDEN));
    }

    return done(null, { isAdmin: true });
  }
}
