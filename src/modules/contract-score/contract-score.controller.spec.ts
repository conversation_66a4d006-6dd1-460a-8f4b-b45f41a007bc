import { Test, TestingModule } from '@nestjs/testing';
import { ContractScoreController } from '@modules/contract-score/contract-score.controller';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import { ScoreRequestDto } from '@modules/contract-score/dto/score.dto';
import { OrderDto } from '@common/dto/order.dto';
import {
  CHART_RANGE,
  ScoreChartParams,
} from '@modules/contract-score/dto/score-history.dto';

describe('ContractScoreController', () => {
  let controller: ContractScoreController;
  let service: ContractScoreService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContractScoreController],
      providers: [
        {
          provide: ContractScoreService,
          useValue: {
            getScore: jest.fn(() => Promise.resolve({})),
            getScoreChartData: jest.fn(() => Promise.resolve({})),
          },
        },
      ],
    }).compile();

    controller = module.get<ContractScoreController>(ContractScoreController);
    service = module.get<ContractScoreService>(ContractScoreService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should pass parameters to service (getScore)', async () => {
    const query: ScoreRequestDto = {
      search: 'Ethereum',
      order: OrderDto.ASC,
      orderBy: 'maturityScore',
      limit: 10,
      offset: 20,
    };

    const getScoreServiceMock = jest
      .spyOn(service, 'getScore')
      .mockImplementation(({ search, order, orderBy, limit, offset }) => {
        expect(search).toBe(query.search);
        expect(order).toBe(query.order);
        expect(orderBy).toBe(query.orderBy);
        expect(limit).toBe(query.limit);
        expect(offset).toBe(query.offset);

        return Promise.resolve({ data: [], offset: 0, limit: 0, total: 0 });
      });

    await controller.getScore(query);
    expect(getScoreServiceMock.mock.calls.length).toBe(1);

    getScoreServiceMock.mockRestore();
  });

  it('should pass parameters to service (getScoreChartData)', async () => {
    const params: ScoreChartParams = {
      contractId: 100,
      chartRange: CHART_RANGE['7D'],
    };

    const getScoreChartDataServiceMock = jest
      .spyOn(service, 'getScoreChartData')
      .mockImplementation(({ contractId, chartRange }) => {
        expect(contractId).toBe(params.contractId);
        expect(chartRange).toBe(params.chartRange);

        return Promise.resolve([]);
      });

    await controller.getScoreChartData(params);
    expect(getScoreChartDataServiceMock.mock.calls.length).toBe(1);

    getScoreChartDataServiceMock.mockRestore();
  });
});
