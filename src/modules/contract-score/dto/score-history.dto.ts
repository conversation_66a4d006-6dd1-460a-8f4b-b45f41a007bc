import { BaseDto } from '@common/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum CHART_RANGE {
  '7D' = '7D',
  '1M' = '1M',
  '3M' = '3M',
  '1Y' = '1Y',
  'All' = 'All',
}

export const CHART_RANGE_DB = {
  '7D': ['day', '-7'],
  '1M': ['month', '-1'],
  '3M': ['month', '-3'],
  '1Y': ['month', '-12'],
  All: [],
};

export class ScoreChartParams {
  @ApiProperty({
    example: 152,
  })
  @Type(() => Number)
  contractId: number;

  @ApiProperty({
    enum: CHART_RANGE,
  })
  chartRange: CHART_RANGE;
}

export interface ScoreChart {
  date: Date;
  score: number;
  pqrScore: number;
  maturity: number;
}

export class GetScoreChartDto extends BaseDto implements ScoreChart {
  @ApiProperty({
    type: Date,
  })
  date: Date;

  @ApiProperty()
  score: number;

  @ApiProperty()
  pqrScore: number;

  @ApiProperty()
  maturity: number;
}
