import { ApiProperty } from '@nestjs/swagger';
import { PagingResponseDto } from '@common/dto/paging-response.dto';
import { GetScoreItemDto } from '@modules/contract-score/dto/score.dto';

export class GetScorePagingDto extends PagingResponseDto<GetScoreItemDto> {
  @ApiProperty({ type: [GetScoreItemDto] })
  data: GetScoreItemDto[];
}

export class ScoreMetadataPagingDto extends PagingResponseDto<string> {
  @ApiProperty({ type: [String] })
  data: string[];
}
