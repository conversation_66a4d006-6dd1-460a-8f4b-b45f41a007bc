import { BaseDto } from '@common/dto/base.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PagingRequestDto } from '@common/dto/paging-request.dto';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';
import { OrderDto } from '@common/dto/order.dto';

export const ScoreRequestOrderByDto = {
  score: 'contract_score',
  pqrScore: 'defisafety_score',
  maturityScore: 'maturity_value_score',
  auditScore: 'audit_score',
  contractAge: 'age_days',
  ctExploits: 'ct_exploits',
  mostRecentExploit: 'most_recent_exploit',
  deployedDate: 'deploy_timestamp',
};

export class ScoreRequestDto extends PagingRequestDto {
  @ApiPropertyOptional({
    description:
      'Could contain contract address (ex: "******************************************") or protocol/chain name (ex: "Ethereum", "Maker DAO"). Registry independent: "Maker DAO" and "mAkEr dao" will provide the same result',
    default: undefined,
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description:
      'List of chains to include (separated by comma). Get all chains by default.',
    example: 'Ethereum,Fantom,Polygon,Avalanche',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  filterChain?: string;

  @ApiPropertyOptional({
    description:
      'List of protocols to include (separated by comma). Get all chains by default. Ex: "Compound,Convex Finance,Curve Finance"',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  filterProtocol?: string;

  @ApiPropertyOptional({
    description:
      'List of contracts to include (separated by comma). Get all contracts by default. Ex: "BabySwap Router,ETH/vETH2-f Deposit/Withdraw"',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  filterContract?: string;

  @ApiPropertyOptional({
    description:
      'List of id\'s to include (separated by comma). Get all contracts by default. Ex: "100,25,1751"',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  filterId?: string;

  @ApiPropertyOptional({
    maximum: 100,
    minimum: 0,
    description:
      'From - to filter for contract score. If provided only one value "to" or "from" means result will be less than "to" or more than "from". If provided both values - result will be between them, INCLUDING! exact value. Minimum value is 0, maximum value is 100.',
  })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  scoreRangeFrom?: number;

  @ApiPropertyOptional({
    maximum: 100,
    minimum: 0,
  })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  scoreRangeTo?: number;

  @ApiPropertyOptional({
    enum: Object.keys(ScoreRequestOrderByDto),
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(Object.keys(ScoreRequestOrderByDto))
  orderBy: string;

  @ApiPropertyOptional({
    enum: OrderDto,
    description: '"DESC" by default (if "order" is provided).',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(OrderDto)
  order: OrderDto;
}

export interface GetScoresItem {
  id: number;
  interactionAddress: string;
  systemId: number;
  chainId: number;
  chainName: string;
  protocolName: string;
  specificContract: string;
  score: number;
  pqrScore: number;
  maturityScore: number;
  auditScore: number;
  contractAge: number;
  deployedDate: Date;
  ctExploits: number;
  mostRecentExploit: Date;
  explorerUrl: string;
}

export class GetScoreItemDto extends BaseDto implements GetScoresItem {
  @ApiProperty()
  id: number;

  @ApiProperty({
    maxLength: 45,
  })
  interactionAddress: string;

  @ApiProperty()
  systemId: number;

  @ApiProperty()
  chainId: number;

  @ApiProperty({
    maxLength: 50,
  })
  chainName: string;

  @ApiProperty({
    maxLength: 50,
  })
  protocolName: string;

  @ApiProperty()
  specificContract: string;

  @ApiProperty()
  score: number;

  @ApiProperty()
  pqrScore: number;

  @ApiProperty()
  maturityScore: number;

  @ApiProperty()
  auditScore: number;

  @ApiProperty()
  contractAge: number;

  @ApiProperty()
  deployedDate: Date;

  @ApiProperty()
  ctExploits: number;

  @ApiProperty()
  mostRecentExploit: Date;

  @ApiProperty()
  explorerUrl: string;
}

export class ScoreCountResponseDto {
  @ApiProperty()
  total: number;
}
