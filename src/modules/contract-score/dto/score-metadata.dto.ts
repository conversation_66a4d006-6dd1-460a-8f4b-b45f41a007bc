import { ApiProperty } from '@nestjs/swagger';
import { PagingRequestDto } from '@common/dto/paging-request.dto';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export const SCORE_METADATA = {
  contract: 'text_name',
  protocol: 'protocol_name',
  chain: 'chain_name',
};

export class ScoreMetadataParams extends PagingRequestDto {
  @ApiProperty({
    description:
      'Could be substring of contract/protocol/chain name (ex: "Pancake swap", "swa"). Registry independent: "Swap" and "sWaP" will provide the same result',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    enum: Object.keys(SCORE_METADATA),
  })
  @IsEnum(Object.keys(SCORE_METADATA))
  type: string;
}
