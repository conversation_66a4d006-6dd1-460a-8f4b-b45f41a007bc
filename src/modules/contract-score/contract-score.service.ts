import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { GetScorePagingDto } from '@modules/contract-score/dto/contract-score-paging.dto';
import {
  CHART_RANGE,
  CHART_RANGE_DB,
  GetScoreChartDto,
  ScoreChartParams,
} from '@modules/contract-score/dto/score-history.dto';
import { ScoreHistoryEntity } from '@modules/contract-score/entities/score-history.entity';
import {
  ScoreCountResponseDto,
  ScoreRequestDto,
  ScoreRequestOrderByDto,
} from '@modules/contract-score/dto/score.dto';
import { OrderDto } from '@common/dto/order.dto';
import { DATA_DB_CONNECTION } from '@config/tokens';
import { PagingResponseDto } from '@common/dto/paging-response.dto';
import {
  SCORE_METADATA,
  ScoreMetadataParams,
} from '@modules/contract-score/dto/score-metadata.dto';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';

@Injectable()
export class ContractScoreService {
  public select = [
    '"id" AS id',
    '"address" AS interactionAddress',
    '"contract_system_dbid" AS systemId',
    '"chainid" AS chainId',
    '"chain_name" AS chainName',
    '"protocol_name" AS protocolName',
    '"text_name" AS specificContract',
    '"contract_score" AS score',
    '"defisafety_score" AS pqrScore',
    '"maturity_value_score" * 100 AS maturityScore',
    '"audit_score" AS auditScore',
    '"age_days" AS contractAge',
    '"deploy_timestamp" AS deployedDate',
    '"ct_exploits" AS ctExploits',
    '"most_recent_exploit" AS mostRecentExploit',
    '"explorer_url" AS explorerUrl',
  ];

  constructor(
    @InjectRepository(ScoreHistoryEntity, DATA_DB_CONNECTION)
    private scoreHistoryRepository: Repository<ScoreHistoryEntity>,
    @InjectRepository(SearchableEntity, DATA_DB_CONNECTION)
    private searchableRepository: Repository<SearchableEntity>,
  ) {}

  async getScore({
    search,
    order,
    orderBy,
    limit = 10,
    offset = 0,
    filterChain,
    filterProtocol,
    filterContract,
    filterId,
    scoreRangeFrom,
    scoreRangeTo,
  }: ScoreRequestDto): Promise<GetScorePagingDto> {
    const qb = this.searchableRepository
      .createQueryBuilder()
      .select(this.select)
      .where('"contract_score" IS NOT NULL')
      .andWhere('"defisafety_score" >= 0')
      .andWhere('"audit_score" >= 0');

    if (search) {
      qb.andWhere(
        new Brackets((qb) => {
          qb.where('"address" LIKE :search', {
            search: `%${search}%`,
          })
            .orWhere('"chain_name" LIKE :search', {
              search: `%${search}%`,
            })
            .orWhere('"protocol_name" LIKE :search', {
              search: `%${search}%`,
            })
            .orWhere('"text_name" LIKE :search', {
              search: `%${search}%`,
            });
        }),
      );
    }

    const splitFilterChain = filterChain ? filterChain.split(',') : [];

    if (splitFilterChain.length) {
      qb.andWhere('"chain_name" IN (:...filterChain)', {
        filterChain: splitFilterChain,
      });
    }

    const splitFilterProtocol = filterProtocol ? filterProtocol.split(',') : [];

    if (splitFilterProtocol.length) {
      qb.andWhere('"protocol_name" IN (:...filterProtocol)', {
        filterProtocol: splitFilterProtocol,
      });
    }

    const splitFilterContract = filterContract ? filterContract.split(',') : [];

    if (splitFilterContract.length) {
      qb.andWhere('"text_name" IN (:...filterContract)', {
        filterContract: splitFilterContract,
      });
    }

    const splitFilterId = filterId ? filterId.split(',') : [];

    if (splitFilterId.length) {
      qb.andWhere('"id" IN (:...filterId)', {
        filterId: splitFilterId,
      });
    }

    if (scoreRangeFrom) {
      qb.andWhere('"contract_score" >= :scoreRangeFrom', {
        scoreRangeFrom,
      });
    }

    if (scoreRangeTo) {
      qb.andWhere('"contract_score" <= :scoreRangeTo', {
        scoreRangeTo,
      });
    }

    if (order) {
      qb.addOrderBy(
        `"${ScoreRequestOrderByDto[orderBy]}"`,
        order || OrderDto.DESC,
      );
    }

    const count = await qb.getCount();
    const res = await qb
      .limit(Number(limit))
      .offset(Number(offset))
      .getRawMany();

    return {
      offset: Number(offset),
      limit: Number(limit),
      total: Number(count),
      data: res,
    };
  }

  async getScoreChartData({
    contractId,
    chartRange,
  }: ScoreChartParams): Promise<Array<GetScoreChartDto> | null> {
    const qb = await this.scoreHistoryRepository
      .createQueryBuilder()
      .select([
        '"date" AS date',
        '"contract_score" AS score',
        '"defisafety_score" AS pqrScore',
        '"maturity_value_score" * 100 AS maturity',
      ])
      .where('"contract_object_dbid" = :contractId', { contractId });

    if (chartRange !== CHART_RANGE.All) {
      qb.andWhere(
        `"date" > DATEADD(${CHART_RANGE_DB[chartRange][0]}, ${CHART_RANGE_DB[chartRange][1]}, SYSDATETIME())`,
      );
    }

    return qb.getRawMany();
  }

  async getMetadata({
    search,
    limit = 10,
    offset = 0,
    type,
  }: ScoreMetadataParams): Promise<PagingResponseDto<string>> {
    const qb = this.searchableRepository.createQueryBuilder();

    const countQb = qb
      .select(`COUNT(DISTINCT "${SCORE_METADATA[type]}")`, 'cnt')
      .where('"contract_score" IS NOT NULL')
      .andWhere('"defisafety_score" >= 0')
      .andWhere('"audit_score" >= 0');

    if (search) {
      countQb.andWhere(`"${SCORE_METADATA[type]}" LIKE :search`, {
        search: `%${search}%`,
      });
    }

    const count = await countQb.getRawOne();

    const resQb = qb
      .select(`"${SCORE_METADATA[type]}"`, 'title')
      .distinct(true)
      .where('"contract_score" IS NOT NULL')
      .andWhere('"defisafety_score" >= 0')
      .andWhere('"audit_score" >= 0');

    if (search) {
      countQb.andWhere(`"${SCORE_METADATA[type]}" LIKE :search`, {
        search: `%${search}%`,
      });
    }

    const res = await resQb
      .addOrderBy('title', OrderDto.ASC)
      .limit(Number(limit))
      .offset(Number(offset))
      .getRawMany();

    return {
      offset: Number(offset),
      limit: Number(limit),
      total: Number(count.cnt),
      data: res.map((item) => item.title),
    };
  }

  async getCount(): Promise<ScoreCountResponseDto> {
    const total = await this.searchableRepository
      .createQueryBuilder()
      .where('"contract_score" IS NOT NULL')
      .andWhere('"defisafety_score" >= 0')
      .andWhere('"audit_score" >= 0')
      .getCount();

    return { total };
  }
}
