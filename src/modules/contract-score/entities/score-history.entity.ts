import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('scores_historical_cached')
export class ScoreHistoryEntity {
  @PrimaryColumn({
    name: 'date',
    type: 'date',
    nullable: false,
  })
  date: Date;

  @PrimaryColumn({
    name: 'contract_object_dbid',
    type: 'int',
    nullable: false,
  })
  contractId: number;

  @Column({
    name: 'tvl_usd',
    type: 'decimal',
    precision: 38,
    scale: 4,
    nullable: true,
  })
  tvlUsd: number;

  @Column({
    name: 'deploy_date',
    type: 'date',
    nullable: true,
  })
  deployDate: Date;

  @Column({
    name: 'last_exploit',
    type: 'date',
    nullable: true,
  })
  lastExploit: Date;

  @Column({
    name: 'age_days',
    type: 'int',
    nullable: true,
  })
  ageDays: number;

  @Column({
    name: 'days_since_exploit',
    type: 'int',
    nullable: true,
  })
  daysSinceExploit: number;

  @Column({
    name: 'days',
    type: 'int',
    nullable: true,
  })
  days: number;

  @Column({
    name: 'immature_days',
    type: 'varchar',
    length: '50',
    nullable: true,
  })
  immatureDays: string;

  @Column({
    name: 'mature_days',
    type: 'varchar',
    length: '50',
    nullable: true,
  })
  matureDays: string;

  @Column({
    name: 'day_multiple',
    type: 'decimal',
    precision: 29,
    scale: 13,
    nullable: true,
  })
  dayMultiple: number;

  @Column({
    name: 'daily_maturity_value',
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  dailyMaturityValue: number;

  @Column({
    name: 'cum_maturity_value',
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  cumMaturityValue: number;

  @Column({
    name: 'max_cumulative_maturity_value',
    type: 'varchar',
    length: '50',
    nullable: true,
  })
  maxCumulativeMaturityValue: string;

  @Column({
    name: 'maturity_value_score',
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  maturityValueScore: number;

  @Column({
    name: 'defisafety_score',
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
  })
  defisafetyScore: number;

  @Column({
    name: 'audit_score',
    type: 'decimal',
    precision: 7,
    scale: 4,
    nullable: true,
  })
  auditScore: number;

  @Column({
    name: 'contract_score_base',
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  contractScoreBase: number;

  @Column({
    name: 'transition_coefficient_1',
    type: 'decimal',
    precision: 26,
    scale: 11,
    nullable: true,
  })
  transitionCoefficient1: number;

  @Column({
    name: 'transition_coefficient_2',
    type: 'decimal',
    precision: 26,
    scale: 11,
    nullable: true,
  })
  transitionCoefficient2: number;

  @Column({
    name: 'contract_score',
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  contractScore: number;
}
