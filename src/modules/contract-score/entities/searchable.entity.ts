import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('searchable')
export class SearchableEntity {
  @PrimaryColumn()
  id: number;

  @Column({
    type: 'varchar',
    length: '45',
  })
  address: string;

  @Column({
    type: 'int',
  })
  contract_system_dbid: number;

  @Column({
    type: 'int',
  })
  chainid: number;

  @Column({
    type: 'varchar',
    length: '50',
    nullable: true,
  })
  chain_name: string;

  @Column({
    type: 'varchar',
    length: '50',
    nullable: true,
  })
  protocol_name: string;

  @Column({
    type: 'varchar',
    length: '100',
    nullable: true,
  })
  system_text_name: string;

  @Column({
    type: 'varchar',
    length: '100',
    nullable: true,
  })
  text_name: string;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  contract_score: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
  })
  defisafety_score: number;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 6,
    nullable: true,
  })
  maturity_value_score: number;

  @Column({
    type: 'decimal',
    precision: 7,
    scale: 4,
    nullable: true,
  })
  audit_score: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  age_days: number;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  deploy_timestamp: Date;

  @Column({
    type: 'int',
    nullable: true,
  })
  ct_exploits: number;

  @Column({
    type: 'date',
    nullable: true,
  })
  most_recent_exploit: Date;

  @Column({
    type: 'varchar',
    length: '308',
    nullable: false,
  })
  explorer_url: string;
}
