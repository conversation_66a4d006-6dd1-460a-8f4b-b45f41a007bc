import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ContractScoreController } from '@modules/contract-score/contract-score.controller';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import { ScoreHistoryEntity } from '@modules/contract-score/entities/score-history.entity';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';
import { DATA_DB_CONNECTION } from '@config/tokens';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [ScoreHistoryEntity, SearchableEntity],
      DATA_DB_CONNECTION,
    ),
  ],
  controllers: [ContractScoreController],
  providers: [ContractScoreService],
  exports: [ContractScoreService],
})
export class ContractScoreModule {}
