import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import {
  GetScorePagingDto,
  ScoreMetadataPagingDto,
} from '@modules/contract-score/dto/contract-score-paging.dto';
import {
  GetScoreChartDto,
  ScoreChartParams,
} from '@modules/contract-score/dto/score-history.dto';
import { ScoreRequestDto } from '@modules/contract-score/dto/score.dto';
import { ScoreMetadataParams } from '@modules/contract-score/dto/score-metadata.dto';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_FORBIDDEN,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { JwtAuthApiKeyGuard } from '@modules/user/jwt-api-key.guard';
import { ApiRole, Role } from '@common/decorators/role.decorator';
import {
  API_PERMISSIONS_LIST,
  PLAN_PERMISSIONS_LIST,
} from '@common/utils/permissions';
import { AuthGuard } from '@modules/user/auth.guard';

@ApiTags('Contract Scores')
@Role(PLAN_PERMISSIONS_LIST.CONTRACT_SCORES)
@ApiRole(API_PERMISSIONS_LIST.CONTRACT_SCORES)
@ApiBearerAuth('api-key')
@ApiSecurity({ 'access-token': [], 'x-application-key': [] })
@UseGuards(JwtAuthApiKeyGuard, AuthGuard)
@Controller('contract-scores')
export class ContractScoreController {
  constructor(private readonly contractScoreService: ContractScoreService) {}

  @Get('')
  @ApiOperation({ summary: 'Get contract scores.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getScore(@Query() query: ScoreRequestDto): Promise<GetScorePagingDto> {
    return this.contractScoreService.getScore(query);
  }

  @Get('metadata')
  @ApiOperation({ summary: 'Get unique metadata values.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getMetadata(
    @Query() params: ScoreMetadataParams,
  ): Promise<ScoreMetadataPagingDto> {
    return this.contractScoreService.getMetadata(params);
  }

  @Get('/:contractId/:chartRange')
  @ApiOperation({
    summary: 'Get historical data (scores) of particular contract score.',
  })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getScoreChartData(
    @Param() params: ScoreChartParams,
  ): Promise<Array<GetScoreChartDto>> {
    return this.contractScoreService.getScoreChartData(params);
  }
}
