import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_FORBIDDEN,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { HttpService } from '@nestjs/axios';
import {
  PqrFullReviewResponseDto,
  PqrRequestDto,
  PqrResponseDto,
  PqrsRequestDto,
  PqrsResponsePagingDto,
} from '@modules/pqr/dto/pqrs.dto';
import { JwtAuthApiKeyGuard } from '@modules/user/jwt-api-key.guard';
import { ApiRole } from '@common/decorators/role.decorator';
import { API_PERMISSIONS_LIST } from '@common/utils/permissions';
import { AuthGuard } from '@modules/user/auth.guard';
import { PqrService } from '@modules/pqr/pqr.service';
import { AccessStrategyDto } from '@modules/user/dto/strategy.dto';

@ApiTags('PQRs')
// @Role(PLAN_PERMISSIONS_LIST.PQRS) // PQR's should be accessible to anyone for free
@ApiRole(API_PERMISSIONS_LIST.PQRS)
@ApiBearerAuth('api-key')
@ApiSecurity({ 'access-token': [], 'x-application-key': [] })
@UseGuards(JwtAuthApiKeyGuard, AuthGuard)
@Controller('pqrs')
export class PqrController {
  constructor(
    private httpService: HttpService,
    private readonly pqrService: PqrService,
  ) {}

  @Get('')
  @ApiOperation({ summary: 'Get PQRs.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getPqrs(
    @Request() { user }: AccessStrategyDto,
    @Query() query: PqrsRequestDto,
  ): Promise<PqrsResponsePagingDto> {
    return this.pqrService.getPqrs(query, user?.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get single PQR by id.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  async getPqr(
    @Request() { user }: AccessStrategyDto,
    @Param() { id }: PqrRequestDto,
  ): Promise<PqrResponseDto> {
    return this.pqrService.getPqr({ id }, user?.id);
  }

  @Get('full/:id')
  @ApiOperation({ summary: 'Get full PQR report by id.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiForbiddenResponse({ description: ERROR_HTTP_FORBIDDEN })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  async getFullPqr(
    @Request() { user }: AccessStrategyDto,
    @Param() { id }: PqrRequestDto,
  ): Promise<PqrFullReviewResponseDto> {
    return this.pqrService.getFullPqr({ id }, user?.id);
  }
}
