import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { PagingRequestDto } from '@common/dto/paging-request.dto';
import { OrderDto } from '@common/dto/order.dto';
import { BaseDto } from '@common/dto/base.dto';
import { PagingResponseDto } from '@common/dto/paging-response.dto';

export enum PqrsRequestOrderByDto {
  version = 'version',
  title = 'title',
  status = 'status',
  reviewStatus = 'reviewStatus',
  overallScore = 'overallScore',
  penaltyScore = 'penaltyScore',
  finalScore = 'finalScore',
  date = 'date',
}

export enum PqrsStatusDto {
  Active = 'Active',
  Retired = 'Retired',
  Upcoming = 'Upcoming',
}

export enum PqrsReviewStatusDto {
  Pending = 'Pending',
  InReview = 'InReview',
  Completed = 'Completed',
}

export class PqrsRequestDto extends PagingRequestDto {
  @ApiPropertyOptional({
    description:
      'Search by project name substring (ex: "shiSwap"). Registry independent: "SushiSwap" and "susHisWAp" will provide the same result',
    default: undefined,
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'List of categories to include. Get all chains by default.',
    isArray: true,
    type: 'integer',
  })
  @IsOptional()
  category?: Array<number> | number;

  @ApiPropertyOptional({
    description: 'List of chains to include. Get all chains by default',
    isArray: true,
    type: 'integer',
  })
  @IsOptional()
  chain?: Array<number> | number;

  @ApiPropertyOptional({
    description: 'List of addresses to include. Get all chains by default',
    isArray: true,
    type: String,
  })
  @IsOptional()
  tokenAddress?: Array<string> | string;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(PqrsStatusDto)
  status?: PqrsStatusDto;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(PqrsReviewStatusDto)
  reviewStatus?: PqrsReviewStatusDto;

  @ApiPropertyOptional()
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(PqrsRequestOrderByDto)
  orderBy?: PqrsRequestOrderByDto;

  @ApiPropertyOptional({
    enum: OrderDto,
    description: '"DESC" by default (if "order" is provided).',
  })
  @Type(() => String)
  @IsOptional()
  @IsString()
  @IsEnum(OrderDto)
  order?: OrderDto;
}

export class PqrBreakdown {
  @ApiProperty({ format: 'uuid' })
  id: string;
  @ApiProperty()
  name: string;
  @ApiProperty()
  percentage: number;
}

export class PqrImage {
  @ApiProperty()
  url: string;
  @ApiProperty()
  width?: number;
  @ApiProperty()
  height?: number;
}

export class PqrBlockchain {
  @ApiProperty()
  id: number;
  @ApiProperty()
  name: string;
  @ApiProperty()
  image: PqrImage | null;
  @ApiProperty()
  chainID?: string;
}

export class PqrsHackHistoryData {
  @ApiProperty()
  id?: number;
  @ApiProperty()
  hackDate: string;
  @ApiProperty()
  hackDetails: string;
  @ApiProperty()
  hackReferenceLink: string;
}

export class PqrResponseDto extends BaseDto {
  @ApiProperty()
  _id: number;
  @ApiProperty()
  version: string;
  @ApiProperty()
  title: string;
  @ApiProperty()
  ethAddress: string;
  @ApiProperty()
  tokenAddress: string;
  @ApiProperty({ enum: PqrsStatusDto })
  status: PqrsStatusDto;
  @ApiProperty({ enum: PqrsReviewStatusDto })
  reviewStatus: PqrsReviewStatusDto;
  @ApiProperty()
  overallScore: number;
  @ApiProperty()
  finalScore: number;
  @ApiProperty()
  penaltyScore?: number;
  @ApiProperty({ format: 'url' })
  imageUrl: string;
  @ApiProperty({ format: 'date' })
  date: string;
  @ApiProperty({ type: [PqrBlockchain] })
  chain: PqrBlockchain[];
  @ApiProperty()
  categories: string[];
  @ApiProperty({ format: 'url' })
  url?: string;
  @ApiProperty({ format: 'url' })
  legacyUrl?: string;
  @ApiProperty({ type: [PqrBreakdown] })
  breakdowns: PqrBreakdown[];
  @ApiProperty({ type: [PqrsHackHistoryData] })
  hackHistory: PqrsHackHistoryData[];
}

export class PqrsResponsePagingDto extends PagingResponseDto<PqrResponseDto> {
  @ApiProperty({ type: [PqrResponseDto] })
  data: PqrResponseDto[];
}

export class PqrRequestDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  id: number;
}

export class PqrAuditQuestionData {
  @ApiProperty()
  id: string;
  @ApiProperty()
  question: string;
  @ApiProperty()
  answer: string | number;
  @ApiProperty()
  justification: string;
  @ApiProperty()
  guidance?: string;
  @ApiProperty()
  deployedContracts?: { url: string };
}

export class PqrAuditSectionData {
  @ApiProperty()
  sectionName: string;
  @ApiProperty()
  totalScore: number;
  @ApiProperty()
  sectionDescription: string;
  @ApiProperty()
  questions: PqrAuditQuestionData[];
}

export class PqrAuditPreviousVersion {
  @ApiProperty()
  id: number;
  @ApiProperty()
  auditVersion: string;
  @ApiProperty()
  totalScore: number;
  @ApiProperty()
  pdfReport?: { url: string };
  @ApiProperty()
  link?: string;
}

export class PqrAuditAppendixTableData {
  @ApiProperty()
  language: string;
  @ApiProperty()
  files: number;
  @ApiProperty()
  lines: number;
  @ApiProperty()
  blanks: number;
  @ApiProperty()
  comments: number;
  @ApiProperty()
  code?: number;
  @ApiProperty()
  testingCode?: number;
  @ApiProperty()
  deployedCode?: number;
  @ApiProperty()
  complexity: number;
}

export class PqrAuditAppendixCode {
  @ApiProperty()
  url: string;
  @ApiProperty()
  width: number;
  @ApiProperty()
  height: number;
}

export class PqrAuditAppendixData {
  @ApiProperty()
  appendixIntroduction?: string;
  @ApiProperty()
  appendixExecutingCode?: PqrAuditAppendixCode | null;
  @ApiProperty()
  appendixExampleCode?: string;
  @ApiProperty()
  appendixCodeUsed?: PqrAuditAppendixCode | null;
  @ApiProperty()
  contractsTable?: PqrAuditAppendixTableData[];
  @ApiProperty()
  testsTable?: PqrAuditAppendixTableData[];
  @ApiProperty()
  appendixSmartContractAddress?: PqrAuditAppendixCode | null;
  @ApiProperty()
  appendixTeam?: PqrAuditAppendixCode | null;
}

export class PqrFullReviewResponseDto {
  @ApiProperty()
  _id: number;
  @ApiProperty()
  title: string;
  @ApiProperty({ format: 'date' })
  date: string;
  @ApiProperty()
  author: string;
  @ApiProperty({ type: [PqrBlockchain] })
  chain: PqrBlockchain[];
  @ApiProperty()
  category: string[];
  @ApiProperty()
  finalScore: number;
  @ApiProperty()
  status: string;
  @ApiProperty()
  notes: string;
  @ApiProperty()
  version: number;
  @ApiProperty({ type: PqrAuditAppendixData })
  appendices: PqrAuditAppendixData;
  @ApiProperty({ type: [PqrsHackHistoryData] })
  hackHistory: PqrsHackHistoryData[];
  @ApiProperty()
  tokenAddress: string;
  @ApiProperty()
  ethAddress: string;
  @ApiProperty()
  reviewStatus: string;
  @ApiProperty()
  auditTemplateVersion: PqrAuditSectionData[];
  @ApiProperty({ type: PqrImage })
  image?: PqrImage | null;
  @ApiProperty({ type: [PqrAuditPreviousVersion] })
  previousReviewVersions: PqrAuditPreviousVersion[];
  @ApiProperty()
  overallScore: number;
  @ApiProperty()
  protocolWebsite: string;
  @ApiProperty()
  aboutProtocol: string;
  @ApiProperty()
  penaltyScore?: number;
}
