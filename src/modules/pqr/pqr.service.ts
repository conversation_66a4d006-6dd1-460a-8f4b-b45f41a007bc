import { Injectable } from '@nestjs/common';
import {
  PqrFullReviewResponseDto,
  PqrRequestDto,
  PqrResponseDto,
  PqrsRequestDto,
  PqrsResponsePagingDto,
} from '@modules/pqr/dto/pqrs.dto';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class PqrService {
  private host = process.env.STRAPI_HOST;
  private token = process.env.STRAPI_BEARER_TOKEN;

  constructor(private readonly httpService: HttpService) {}

  async getPqrs(
    query: PqrsRequestDto,
    userId = '',
  ): Promise<PqrsResponsePagingDto> {
    const { order, orderBy, search, ...params } = query;
    const res = await this.httpService
      .get(`${this.host}/pqrs`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          'x-app-user-id': userId,
          'x-app-user-access': 'true',
        },
        params: { ...params, sortBy: orderBy, sortOrder: order, title: search },
      })
      .toPromise();

    return {
      offset: Number(query.offset) || 0,
      limit: Number(query.limit) || 10,
      total: res.data.total,
      data: res.data.data,
    };
  }

  async getPqr({ id }: PqrRequestDto, userId = ''): Promise<PqrResponseDto> {
    const res = await this.httpService
      .get(`${this.host}/pqrs/${id}`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          'x-app-user-id': userId,
          'x-app-user-access': 'true',
        },
      })
      .toPromise();

    return res.data;
  }

  async getFullPqr(
    { id }: PqrRequestDto,
    userId = '',
  ): Promise<PqrFullReviewResponseDto> {
    const res = await this.httpService
      .get(`${this.host}/pqrs/full/${id}`, {
        headers: {
          Authorization: `Bearer ${this.token}`,
          'x-app-user-id': userId,
          'x-app-user-access': 'true',
        },
      })
      .toPromise();

    return res.data;
  }
}
