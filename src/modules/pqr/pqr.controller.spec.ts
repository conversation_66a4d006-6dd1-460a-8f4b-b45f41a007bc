import { Test, TestingModule } from '@nestjs/testing';
import { Pqr<PERSON>ontroller } from './pqr.controller';
import { HttpModule } from '@nestjs/axios';
import { PqrService } from '@modules/pqr/pqr.service';

describe('PqrController', () => {
  let controller: PqrController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PqrController],
      imports: [HttpModule],
      providers: [PqrService],
    }).compile();

    controller = module.get<PqrController>(PqrController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
