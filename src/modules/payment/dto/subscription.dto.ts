import { ApiProperty } from '@nestjs/swagger';

export interface SubscriptionDto {
  userId: string;
}

export class SubscriptionDtoResponse {
  @ApiProperty()
  name: string;

  @ApiProperty({
    enum: ['month', 'year'],
  })
  period: 'day' | 'month' | 'week' | 'year';

  @ApiProperty({
    enum: ['fiat', 'crypto'],
  })
  paymentMethod: 'fiat' | 'crypto';

  @ApiProperty({
    type: Date,
  })
  periodEnds: Date;

  @ApiProperty({
    description: 'In coins. Probably you want to divide it to 100.',
  })
  amount: number;

  @ApiProperty({
    description:
      'List of available API access ["PQR\'s", "Contract Scores", "Chain Scores"]',
    type: [String],
  })
  apiAccess: string[];

  @ApiProperty()
  cancelAtPeriodEnd: boolean;

  @ApiProperty({
    type: Date,
  })
  cancelAt: Date;
}
