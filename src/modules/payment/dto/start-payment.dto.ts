import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsEnum, IsNotEmpty } from 'class-validator';
import { UnfinishedPaymentDetails } from '../interfaces/unfinished-payment.interface';
import { BillingCycle } from '../enums/billing-cycle.enum';

export class StartPaymentDto implements UnfinishedPaymentDetails {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  plan: string;

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  productIds: string[];

  @ApiProperty()
  @IsEnum(BillingCycle)
  @IsNotEmpty()
  billingCycle: BillingCycle;
}
