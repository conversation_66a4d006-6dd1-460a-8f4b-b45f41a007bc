import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import {
  UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN,
} from '@modules/user/tokens';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_PRIVATE_ADDRESS_1,
  TEST_DEFAULT_PUBLIC_ADDRESS_1,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { User } from '@root/internal-db/models/User';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { UserService } from '@modules/user/user.service';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';
import Web3 from 'web3';
import { UnverifiedPublicAddressRepository } from '@modules/user/unverified-public-address.repository';
import { PENDING_TRANSACTION_REPOSITORY_TOKEN } from '@modules/payment-crypto/tokens';
import { PendingTransactionRepository } from '@modules/payment-crypto/pending-transactions.repository';

describe('Check current subscription', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let userRepository: UserRepository;
  let user1: User;
  let user2: User;
  let paymentGatewayService: PaymentGatewayService;
  let unverifiedPublicAddressRepository: UnverifiedPublicAddressRepository;
  let pendingTransactionRepository: PendingTransactionRepository;
  let user1Tokens: SignInResponseDto;
  let user2Tokens: SignInResponseDto;

  const payerId = 'cus_LVyPkFg6Idkk5g';
  const binaryPermissions = '011';
  const binaryApiPermissions = '100';
  const after10days = new Date(
    Date.now() + 1000 /*sec*/ * 60 /*min*/ * 60 /*hour*/ * 24 /*day*/ * 10,
  );
  const priceId = 'price_1KuaIzB29B3waVVFdPYfhV51';
  const transactionId =
    '0xe301bb70bcb4210befda9aa3b50ce82240f6fc4edc3c4b105b1db9fc39fca3e8';
  const subscriptionName = 'Low';
  const paymentPeriod = 'month';
  const paymentAmount = 7000;

  const planDetailsData = {
    id: 'price_1KidNSB29B3waVVFC9HmTxQ5',
    object: 'price',
    active: true,
    billing_scheme: 'per_unit',
    created: 1648553902,
    currency: 'usd',
    livemode: false,
    lookup_key: null,
    metadata: {},
    nickname: null,
    product: {
      id: 'prod_LPSI8XVkJWbl8l',
      object: 'product',
      active: true,
      attributes: [],
      created: 1648553902,
      default_price: null,
      description: 'Chain Scores on web',
      images: [],
      livemode: false,
      metadata: {
        'Analyst Support': 'Private Discord Support Channel',
        'Private Reports': 'Quoted Separately',
        Order: '1',
        Access: binaryPermissions,
        'API Access': binaryApiPermissions,
        Type: 'Plan',
        Seats: '1',
      },
      name: subscriptionName,
      package_dimensions: null,
      shippable: null,
      statement_descriptor: null,
      tax_code: null,
      type: 'service',
      unit_label: null,
      updated: **********,
      url: null,
    },
    recurring: {
      aggregate_usage: null,
      interval: paymentPeriod,
      interval_count: 1,
      trial_period_days: null,
      usage_type: 'licensed',
    },
    tax_behavior: 'unspecified',
    tiers_mode: null,
    transform_quantity: null,
    type: 'recurring',
    unit_amount: paymentAmount,
    unit_amount_decimal: paymentAmount.toString(),
  };

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    userService = module.get(UserService);
    unverifiedPublicAddressRepository = module.get(
      UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
    );
    paymentGatewayService = module.get<PaymentGatewayService>(
      PaymentGatewayService,
    );
    pendingTransactionRepository = module.get(
      PENDING_TRANSACTION_REPOSITORY_TOKEN,
    );

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    user1Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });

    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });
    const web3 = new Web3();
    const { signature } = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    user2Tokens = await userService.web3SignIn({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      signature,
    });
    user2 = await userRepository.findOne({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });
  });

  it('should return free tier, because no subscriptions', async () => {
    const getSubscriptionsMock = jest
      .spyOn(paymentGatewayService, 'getSubscriptions')
      .mockImplementation(() => Promise.resolve({ data: [] }) as any);

    const {
      status,
      body: { name, period, paymentMethod, periodEnds, amount },
    } = await supertest
      .agent(app.getHttpServer())
      .get('/payment/subscription')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(name).toBe('Free');
    expect(period).toBeNull();
    expect(paymentMethod).toBeNull();
    expect(periodEnds).toBeNull();
    expect(amount).toBeNull();

    getSubscriptionsMock.mockRestore();
  });

  it('should fulfill subscription by crypto payment', async () => {
    const getSubscriptionsMock = jest
      .spyOn(paymentGatewayService, 'getSubscriptions')
      .mockImplementation(() => Promise.resolve({ data: [] }) as any);

    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId,
      priceId: priceId,
      blockchain: 'eth',
      contract: 'usdt',
    });

    await pendingTransactionRepository.markProcessed({
      id: pendingTransaction.id,
    });
    await userRepository.updateSubscription({
      updateBy: { id: user1.id },
      fieldsToUpdate: {
        permissions: parseInt(binaryPermissions, 2),
        permissionExpiration: after10days,
        apiPermissions: parseInt(binaryApiPermissions, 2),
        apiPermissionExpiration: after10days,
      },
    });

    const getPlanDetailsMock = jest
      .spyOn(paymentGatewayService, 'getPlanDetails')
      .mockImplementation(() => Promise.resolve(planDetailsData as any));

    const {
      status,
      body: { name, period, paymentMethod, periodEnds, amount },
    } = await supertest
      .agent(app.getHttpServer())
      .get('/payment/subscription')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(name).toBe(subscriptionName);
    expect(period).toBe(paymentPeriod);
    expect(paymentMethod).toBe('crypto');
    expect(new Date(periodEnds).getTime()).toBe(after10days.getTime());
    expect(amount).toBe(paymentAmount);

    getPlanDetailsMock.mockRestore();
    await userRepository.updateSubscription({
      updateBy: { id: user1.id },
      fieldsToUpdate: {
        permissions: 0,
        permissionExpiration: null,
        apiPermissions: 0,
        apiPermissionExpiration: null,
      },
    });
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
    getSubscriptionsMock.mockRestore();
  });

  it('should fulfill subscription by fiat payment', async () => {
    const getSubscriptionsMock = jest
      .spyOn(paymentGatewayService, 'getSubscriptions')
      .mockImplementation(
        () =>
          Promise.resolve({
            data: [
              {
                id: 'sub_1KtoV4B29B3waVVFSNbmtEsB',
                created: 1651218266,
                current_period_end: 1653810266,
                current_period_start: 1651218266,
                customer: 'cus_LXgeNtljJtUC25',
                items: {
                  object: 'list',
                  data: [
                    {
                      price: {
                        id: priceId,
                      },
                    },
                  ],
                },
              },
            ],
          }) as any,
      );

    await userRepository.assignPayerId({ id: user2.id, payerId: payerId });
    await userRepository.updateSubscription({
      updateBy: { id: user2.id },
      fieldsToUpdate: {
        permissions: parseInt(binaryPermissions, 2),
        permissionExpiration: after10days,
        apiPermissions: parseInt(binaryApiPermissions, 2),
        apiPermissionExpiration: after10days,
      },
    });

    const getPlanDetailsMock = jest
      .spyOn(paymentGatewayService, 'getPlanDetails')
      .mockImplementation(() => Promise.resolve(planDetailsData as any));

    const {
      status,
      body: { name, period, paymentMethod, periodEnds, amount },
    } = await supertest
      .agent(app.getHttpServer())
      .get('/payment/subscription')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user2Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(name).toBe(subscriptionName);
    expect(period).toBe(paymentPeriod);
    expect(paymentMethod).toBe('fiat');
    expect(new Date(periodEnds).getTime()).toBe(after10days.getTime());
    expect(amount).toBe(paymentAmount);

    getPlanDetailsMock.mockRestore();
    await userRepository.updateSubscription({
      updateBy: { id: user2.id },
      fieldsToUpdate: {
        permissions: 0,
        permissionExpiration: null,
        apiPermissions: 0,
        apiPermissionExpiration: null,
      },
    });
    getSubscriptionsMock.mockRestore();
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
