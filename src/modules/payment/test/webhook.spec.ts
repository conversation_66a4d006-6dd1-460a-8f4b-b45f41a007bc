import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import { ERROR_HTTP_BAD_REQUEST } from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { User } from '@root/internal-db/models/User';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';

describe('Payment webhook', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userRepository: UserRepository;
  let user1: User;
  let user2: User;
  let paymentGatewayService: PaymentGatewayService;

  const payerId = 'cus_LVyPkFg6Idkk5g';
  const payerId2 = 'cus_LVyPkFg6Idkk5f';
  const priceId = 'price_1Kidg2B29B3waVVFDGyXlffY';
  const productId = 'prod_LPSb0S9O9qlbZm';
  const binaryPermissions = '011';
  const binaryApiPermissions = '100';
  const periodEnd = 1681825658;
  const after10days = new Date(
    Date.now() + 1000 /*sec*/ * 60 /*min*/ * 60 /*hour*/ * 24 /*day*/ * 10,
  );
  const after2days = new Date(
    Date.now() + 1000 /*sec*/ * 60 /*min*/ * 60 /*hour*/ * 24 /*day*/ * 2,
  );

  const productData = {
    id: productId,
    object: 'product',
    active: true,
    attributes: [],
    created: 1648555053,
    description:
      "* Contract Scores on web \\n * Chain Scores on web \\n * PQR's with Chain Scores for web \\n * API access to PQR's",
    images: [],
    livemode: false,
    metadata: {
      'Analyst Support':
        'Private API support discord Channel; Up to 2 hours per quarter',
      'Private Reports': 'Quoted separately',
      Order: '2',
      Access: binaryPermissions,
      'API Access': binaryApiPermissions,
    },
    name: 'Medium',
    package_dimensions: null,
    shippable: null,
    statement_descriptor: null,
    tax_code: null,
    type: 'service',
    unit_label: null,
    updated: **********,
    url: null,
  };
  const invoicePaidData = {
    object: {
      id: 'in_1KpuvWB29B3waVVFzDdLA9kk',
      object: 'invoice',
      account_country: 'US',
      account_name: 'TEST',
      account_tax_ids: null,
      amount_due: 480000,
      amount_paid: 480000,
      amount_remaining: 0,
      application_fee_amount: null,
      attempt_count: 1,
      attempted: true,
      auto_advance: false,
      automatic_tax: { enabled: false, status: null },
      billing_reason: 'subscription_create',
      charge: 'ch_3KpuvWB29B3waVVF1h5yN94Y',
      collection_method: 'charge_automatically',
      created: **********,
      currency: 'usd',
      custom_fields: null,
      customer: payerId,
      customer_address: {
        city: null,
        country: 'NO',
        line1: null,
        line2: null,
        postal_code: null,
        state: null,
      },
      customer_email: TEST_DEFAULT_EMAIL_1,
      customer_name: 'Antony Newton',
      customer_phone: null,
      customer_shipping: null,
      customer_tax_exempt: 'none',
      customer_tax_ids: [],
      default_payment_method: null,
      default_source: null,
      default_tax_rates: [],
      description: null,
      discount: null,
      discounts: [],
      due_date: null,
      ending_balance: 0,
      footer: null,
      hosted_invoice_url:
        'https://invoice.stripe.com/i/acct_1KgRrnB29B3waVVF/test_YWNjdF8xS2dScm5CMjlCM3dhVlZGLF9MV3l0WjEzWDlwSXRKaUF0NzdTcjh5WWFjOXFCYjdjLDQwODMwNDYx0200PM6ymzjW?s=ap',
      invoice_pdf:
        'https://pay.stripe.com/invoice/acct_1KgRrnB29B3waVVF/test_YWNjdF8xS2dScm5CMjlCM3dhVlZGLF9MV3l0WjEzWDlwSXRKaUF0NzdTcjh5WWFjOXFCYjdjLDQwODMwNDYx0200PM6ymzjW/pdf?s=ap',
      last_finalization_error: null,
      lines: {
        object: 'list',
        data: [
          {
            id: 'il_1KpuvWB29B3waVVF3v3kLQUQ',
            object: 'line_item',
            amount: 480000,
            currency: 'usd',
            description: '1 × Medium (at $4,800.00 / year)',
            discount_amounts: [],
            discountable: true,
            discounts: [],
            livemode: false,
            metadata: {},
            period: { end: periodEnd, start: ********** },
            plan: {
              id: priceId,
              object: 'plan',
              active: true,
              aggregate_usage: null,
              amount: 480000,
              amount_decimal: '480000',
              billing_scheme: 'per_unit',
              created: **********,
              currency: 'usd',
              interval: 'year',
              interval_count: 1,
              livemode: false,
              metadata: {},
              nickname: null,
              product: productId,
              tiers_mode: null,
              transform_usage: null,
              trial_period_days: null,
              usage_type: 'licensed',
            },
            price: {
              id: priceId,
              object: 'price',
              active: true,
              billing_scheme: 'per_unit',
              created: **********,
              currency: 'usd',
              livemode: false,
              lookup_key: null,
              metadata: {},
              nickname: null,
              product: productId,
              recurring: {
                aggregate_usage: null,
                interval: 'year',
                interval_count: 1,
                trial_period_days: null,
                usage_type: 'licensed',
              },
              tax_behavior: 'unspecified',
              tiers_mode: null,
              transform_quantity: null,
              type: 'recurring',
              unit_amount: 480000,
              unit_amount_decimal: '480000',
            },
            proration: false,
            proration_details: { credited_items: null },
            quantity: 1,
            subscription: 'sub_1KpuvWB29B3waVVFoOtou2qj',
            subscription_item: 'si_LWytqpciUWE7AB',
            tax_amounts: [],
            tax_rates: [],
            type: 'subscription',
          },
        ],
        has_more: false,
        total_count: 1,
        url: '/v1/invoices/in_1KpuvWB29B3waVVFzDdLA9kk/lines',
      },
      livemode: false,
      metadata: {},
      next_payment_attempt: null,
      number: 'E6F7B9D5-0001',
      on_behalf_of: null,
      paid: true,
      paid_out_of_band: false,
      payment_intent: 'pi_3KpuvWB29B3waVVF1GEnjdY5',
      payment_settings: {
        payment_method_options: null,
        payment_method_types: null,
      },
      period_end: **********,
      period_start: **********,
      post_payment_credit_notes_amount: 0,
      pre_payment_credit_notes_amount: 0,
      quote: null,
      receipt_number: null,
      starting_balance: 0,
      statement_descriptor: null,
      status: 'paid',
      status_transitions: {
        finalized_at: **********,
        marked_uncollectible_at: null,
        paid_at: **********,
        voided_at: null,
      },
      subscription: 'sub_1KpuvWB29B3waVVFoOtou2qj',
      subtotal: 480000,
      tax: null,
      test_clock: null,
      total: 480000,
      total_discount_amounts: [],
      total_tax_amounts: [],
      transfer_data: null,
      webhooks_delivered_at: null,
    },
  };

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    paymentGatewayService = module.get<PaymentGatewayService>(
      PaymentGatewayService,
    );

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });

    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    // user3Web3 = await userRepository.upsert({ publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1 });
  });

  it('should create payment session successfully', async () => {
    const webhookData = {
      object: {
        id: 'cs_test_a16hSqX94obm05A5WEdGsYdipcLACNCiJkeQXVziHdfxD0N8xyisBwCObE',
        object: 'checkout.session',
        after_expiration: null,
        allow_promotion_codes: null,
        amount_subtotal: 84000,
        amount_total: 84000,
        automatic_tax: { enabled: false, status: null },
        billing_address_collection: null,
        cancel_url: 'http://localhost:3128/payment_fail',
        client_reference_id: user1.id,
        consent: null,
        consent_collection: null,
        currency: 'usd',
        customer: payerId,
        customer_creation: 'always',
        customer_details: {
          address: {
            city: null,
            country: 'UA',
            line1: null,
            line2: null,
            postal_code: null,
            state: null,
          },
          email: TEST_DEFAULT_EMAIL_1,
          name: 'Jack Sparrow',
          phone: null,
          tax_exempt: 'none',
          tax_ids: [],
        },
        customer_email: TEST_DEFAULT_EMAIL_1,
        expires_at: 1650143609,
        livemode: false,
        locale: null,
        metadata: {},
        mode: 'subscription',
        payment_intent: null,
        payment_link: null,
        payment_method_options: null,
        payment_method_types: ['card'],
        payment_status: 'paid',
        phone_number_collection: { enabled: false },
        recovered_from: null,
        setup_intent: null,
        shipping: null,
        shipping_address_collection: null,
        shipping_options: [],
        shipping_rate: null,
        status: 'complete',
        submit_type: null,
        subscription: 'sub_1KowSoB29B3waVVFPOfhh49k',
        success_url: 'http://localhost:3128/payment_success',
        total_details: {
          amount_discount: 0,
          amount_shipping: 0,
          amount_tax: 0,
        },
        url: null,
      },
    };
    const checkWebhookSignatureMock = jest
      .spyOn(paymentGatewayService, 'checkWebhookSignature')
      .mockImplementationOnce(() =>
        Promise.resolve({
          type: 'checkout.session.completed',
          data: webhookData,
        }),
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          type: 'checkout.session.completed',
          data: webhookData,
        }),
      )
      .mockImplementation(() =>
        Promise.resolve({
          type: 'checkout.session.completed',
          data: {
            ...webhookData,
            object: { ...webhookData.object, customer: payerId2 },
          },
        }),
      );

    const res1 = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(res1.status).toBe(200);
    expect(res1.body.success).toBe(true);

    let user = await userRepository.findOne({ id: user1.id });

    expect(user.payerId).toBe(payerId);

    const res2SamePayerId = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(res2SamePayerId.status).toBe(200);
    expect(res2SamePayerId.body.success).toBe(true);
    user = await userRepository.findOne({ id: user1.id });
    expect(user.payerId).toBe(payerId);

    const res3ChangePayerId = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(res3ChangePayerId.status).toBe(200);
    expect(res3ChangePayerId.body.success).toBe(true);
    user = await userRepository.findOne({ id: user1.id });
    expect(user.payerId).toBe(payerId2);

    checkWebhookSignatureMock.mockRestore();
  });

  it('should handle the payment successfully', async () => {
    await userRepository.assignPayerId({ id: user1.id, payerId: payerId });
    await userRepository.assignPayerId({ id: user2.id, payerId: payerId2 });

    const checkWebhookSignatureMock = jest
      .spyOn(paymentGatewayService, 'checkWebhookSignature')
      .mockImplementation(() =>
        Promise.resolve({
          type: 'invoice.paid',
          data: invoicePaidData,
        }),
      );
    const getProductByIdMock = jest
      .spyOn(paymentGatewayService, 'getProductById')
      .mockImplementation(() => Promise.resolve(productData as any));

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(status).toBe(200);
    expect(body.success).toBe(true);

    const user1Db = await userRepository.findOne({ id: user1.id });

    expect(user1Db.permissions).toBe(3); //integer representation of '011'
    expect(user1Db.apiPermissions).toBe(4); //integer representation of '100'
    expect(user1Db.apiPermissionToken.length).toBe(64);
    expect(new Date(user1Db.permissionExpiration).getTime()).toBe(
      periodEnd * 1000,
    );
    expect(new Date(user1Db.apiPermissionExpiration).getTime()).toBe(
      periodEnd * 1000,
    );

    const user2Db = await userRepository.findOne({ id: user2.id });

    expect(user2Db.permissions).toBe(0);

    await userRepository.updateSubscription({
      updateBy: { payerId },
      fieldsToUpdate: {
        permissions: 0,
        permissionExpiration: null,
        apiPermissions: 0,
        apiPermissionExpiration: null,
      },
    });
    checkWebhookSignatureMock.mockRestore();
    getProductByIdMock.mockRestore();
  });

  it('should throw an error, because no Access metadata configured', async () => {
    await userRepository.assignPayerId({ id: user1.id, payerId: payerId });
    await userRepository.assignPayerId({ id: user2.id, payerId: payerId2 });

    const checkWebhookSignatureMock = jest
      .spyOn(paymentGatewayService, 'checkWebhookSignature')
      .mockImplementation(() =>
        Promise.resolve({
          type: 'invoice.paid',
          data: invoicePaidData,
        }),
      );
    const getProductByIdMock = jest
      .spyOn(paymentGatewayService, 'getProductById')
      .mockImplementation(() =>
        Promise.resolve({
          ...productData,
          metadata: { ...productData.metadata, Access: undefined },
        } as any),
      );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    checkWebhookSignatureMock.mockRestore();
    getProductByIdMock.mockRestore();
  });

  it('should immediately cancel the subscription', async () => {
    await userRepository.assignPayerId({ id: user1.id, payerId: payerId });
    await userRepository.assignPayerId({ id: user2.id, payerId: payerId2 });
    await userRepository.updateSubscription({
      updateBy: { payerId },
      fieldsToUpdate: {
        permissions: 1,
        permissionExpiration: after10days,
        apiPermissions: 2,
        apiPermissionExpiration: after10days,
      },
    });
    await userRepository.updateSubscription({
      updateBy: { payerId: payerId2 },
      fieldsToUpdate: {
        permissions: 3,
        permissionExpiration: after10days,
        apiPermissions: 4,
        apiPermissionExpiration: after10days,
      },
    });

    const cancelSubscriptionData = {
      object: {
        id: 'sub_1KqCBEB29B3waVVFOCTchWMd',
        object: 'subscription',
        application_fee_percent: null,
        automatic_tax: { enabled: false },
        billing_cycle_anchor: **********,
        billing_thresholds: null,
        cancel_at: null,
        cancel_at_period_end: false,
        canceled_at: 1650438271,
        collection_method: 'charge_automatically',
        created: **********,
        current_period_end: 1681891980,
        current_period_start: **********,
        customer: payerId,
        days_until_due: null,
        default_payment_method: 'pm_1KqC99B29B3waVVFTzQJo0Dh',
        default_source: null,
        default_tax_rates: [],
        discount: null,
        ended_at: 1650438271,
        items: {
          object: 'list',
          data: [
            {
              id: 'si_LXGizU5zVMiRgq',
              object: 'subscription_item',
              billing_thresholds: null,
              created: **********,
              metadata: {},
              plan: {
                id: priceId,
                object: 'plan',
                active: true,
                aggregate_usage: null,
                amount: 600000,
                amount_decimal: '600000',
                billing_scheme: 'per_unit',
                created: **********,
                currency: 'usd',
                interval: 'year',
                interval_count: 1,
                livemode: false,
                metadata: {},
                nickname: null,
                product: productId,
                tiers_mode: null,
                transform_usage: null,
                trial_period_days: null,
                usage_type: 'licensed',
              },
              price: {
                id: priceId,
                object: 'price',
                active: true,
                billing_scheme: 'per_unit',
                created: **********,
                currency: 'usd',
                livemode: false,
                lookup_key: null,
                metadata: {},
                nickname: null,
                product: productId,
                recurring: {
                  aggregate_usage: null,
                  interval: 'year',
                  interval_count: 1,
                  trial_period_days: null,
                  usage_type: 'licensed',
                },
                tax_behavior: 'unspecified',
                tiers_mode: null,
                transform_quantity: null,
                type: 'recurring',
                unit_amount: 600000,
                unit_amount_decimal: '600000',
              },
              quantity: 1,
              subscription: 'sub_1KqCBEB29B3waVVFOCTchWMd',
              tax_rates: [],
            },
          ],
          has_more: false,
          total_count: 1,
          url: '/v1/subscription_items?subscription=sub_1KqCBEB29B3waVVFOCTchWMd',
        },
        latest_invoice: 'in_1KqCBEB29B3waVVFgkVRZXLq',
        livemode: false,
        metadata: {},
        next_pending_invoice_item_invoice: null,
        pause_collection: null,
        payment_settings: {
          payment_method_options: null,
          payment_method_types: null,
        },
        pending_invoice_item_interval: null,
        pending_setup_intent: null,
        pending_update: null,
        plan: {
          id: priceId,
          object: 'plan',
          active: true,
          aggregate_usage: null,
          amount: 600000,
          amount_decimal: '600000',
          billing_scheme: 'per_unit',
          created: **********,
          currency: 'usd',
          interval: 'year',
          interval_count: 1,
          livemode: false,
          metadata: {},
          nickname: null,
          product: productId,
          tiers_mode: null,
          transform_usage: null,
          trial_period_days: null,
          usage_type: 'licensed',
        },
        quantity: 1,
        schedule: null,
        start_date: **********,
        status: 'canceled',
        test_clock: null,
        transfer_data: null,
        trial_end: null,
        trial_start: null,
      },
    };

    const checkWebhookSignatureMock = jest
      .spyOn(paymentGatewayService, 'checkWebhookSignature')
      .mockImplementationOnce(() =>
        Promise.resolve({
          type: 'customer.subscription.deleted',
          data: cancelSubscriptionData,
        }),
      )
      .mockImplementation(() =>
        Promise.resolve({
          type: 'customer.subscription.deleted',
          data: {
            object: { ...cancelSubscriptionData.object, customer: payerId2 },
          },
        }),
      );
    const getProductByIdMock = jest
      .spyOn(paymentGatewayService, 'getProductById')
      .mockImplementationOnce(() =>
        Promise.resolve({
          ...productData,
          metadata: productData.metadata,
        } as any),
      )
      .mockImplementation(() =>
        Promise.resolve({
          ...productData,
          metadata: { ...productData.metadata, Type: 'API' },
        } as any),
      );

    const res1 = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(res1.status).toBe(200);
    expect(res1.body.success).toBe(true);

    const user1Db = await userRepository.findOne({ id: user1.id });

    expect(user1Db.permissions).toBe(0);
    expect(user1Db.permissionExpiration).toBeNull();
    expect(user1Db.apiPermissions).toBe(0);
    expect(user1Db.apiPermissionExpiration).toBeNull();

    const user2Db = await userRepository.findOne({ id: user2.id });

    expect(user2Db.permissions).toBe(3);
    expect(user2Db.permissionExpiration).not.toBeNull();
    expect(user2Db.apiPermissions).toBe(4);
    expect(user2Db.apiPermissionExpiration).not.toBeNull();

    await userRepository.updateSubscription({
      updateBy: { payerId: payerId2 },
      fieldsToUpdate: { permissions: 0, permissionExpiration: null },
    });
    checkWebhookSignatureMock.mockRestore();
    getProductByIdMock.mockRestore();
  });

  it('should update expiration time of the subscription', async () => {
    await userRepository.assignPayerId({ id: user1.id, payerId: payerId });
    await userRepository.updateSubscription({
      updateBy: { payerId },
      fieldsToUpdate: {
        permissions: 3,
        permissionExpiration: after10days,
        apiPermissions: 4,
        apiPermissionExpiration: after10days,
      },
    });

    const updateSubscriptionData = {
      object: {
        id: 'sub_1KqEQXB29B3waVVFvYr0lLWN',
        object: 'subscription',
        application_fee_percent: null,
        automatic_tax: { enabled: false },
        billing_cycle_anchor: **********,
        billing_thresholds: null,
        cancel_at: Math.floor(after2days.getTime() / 1000),
        cancel_at_period_end: false,
        canceled_at: 1650441429,
        collection_method: 'charge_automatically',
        created: **********,
        current_period_end: 1650618000,
        current_period_start: **********,
        customer: payerId,
        days_until_due: null,
        default_payment_method: 'pm_1KqEQVB29B3waVVF1qwLNBVE',
        default_source: null,
        default_tax_rates: [],
        discount: null,
        ended_at: null,
        items: {
          object: 'list',
          data: [
            {
              id: 'si_LXJ2rbjzMsbYuc',
              object: 'subscription_item',
              billing_thresholds: null,
              created: 1650364617,
              metadata: {},
              plan: {
                id: priceId,
                object: 'plan',
                active: true,
                aggregate_usage: null,
                amount: 480000,
                amount_decimal: '480000',
                billing_scheme: 'per_unit',
                created: **********,
                currency: 'usd',
                interval: 'year',
                interval_count: 1,
                livemode: false,
                metadata: {},
                nickname: null,
                product: productId,
                tiers_mode: null,
                transform_usage: null,
                trial_period_days: null,
                usage_type: 'licensed',
              },
              price: {
                id: priceId,
                object: 'price',
                active: true,
                billing_scheme: 'per_unit',
                created: **********,
                currency: 'usd',
                livemode: false,
                lookup_key: null,
                metadata: {},
                nickname: null,
                product: productId,
                recurring: {
                  aggregate_usage: null,
                  interval: 'year',
                  interval_count: 1,
                  trial_period_days: null,
                  usage_type: 'licensed',
                },
                tax_behavior: 'unspecified',
                tiers_mode: null,
                transform_quantity: null,
                type: 'recurring',
                unit_amount: 480000,
                unit_amount_decimal: '480000',
              },
              quantity: 1,
              subscription: 'sub_1KqEQXB29B3waVVFvYr0lLWN',
              tax_rates: [],
            },
          ],
          has_more: false,
          total_count: 1,
          url: '/v1/subscription_items?subscription=sub_1KqEQXB29B3waVVFvYr0lLWN',
        },
        latest_invoice: 'in_1KqEQXB29B3waVVFqE9GrH1M',
        livemode: false,
        metadata: {},
        next_pending_invoice_item_invoice: null,
        pause_collection: null,
        payment_settings: {
          payment_method_options: null,
          payment_method_types: null,
        },
        pending_invoice_item_interval: null,
        pending_setup_intent: null,
        pending_update: null,
        plan: {
          id: priceId,
          object: 'plan',
          active: true,
          aggregate_usage: null,
          amount: 480000,
          amount_decimal: '480000',
          billing_scheme: 'per_unit',
          created: **********,
          currency: 'usd',
          interval: 'year',
          interval_count: 1,
          livemode: false,
          metadata: {},
          nickname: null,
          product: productId,
          tiers_mode: null,
          transform_usage: null,
          trial_period_days: null,
          usage_type: 'licensed',
        },
        quantity: 1,
        schedule: null,
        start_date: **********,
        status: 'active',
        test_clock: null,
        transfer_data: null,
        trial_end: null,
        trial_start: null,
      },
      previous_attributes: {
        cancel_at: null,
        canceled_at: null,
        current_period_end: **********,
      },
    };

    const checkWebhookSignatureMock = jest
      .spyOn(paymentGatewayService, 'checkWebhookSignature')
      .mockImplementation(() =>
        Promise.resolve({
          type: 'customer.subscription.updated',
          data: updateSubscriptionData,
        }),
      );
    const getProductByIdMock = jest
      .spyOn(paymentGatewayService, 'getProductById')
      .mockImplementation(() =>
        Promise.resolve({
          ...productData,
          metadata: productData.metadata,
        } as any),
      );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/payment/webhook')
      .send();

    expect(status).toBe(200);
    expect(body.success).toBe(true);

    const user1Db = await userRepository.findOne({ id: user1.id });

    expect(user1Db.permissions).toBe(3);
    expect(user1Db.apiPermissions).toBe(4);

    const expirationDate = new Date(user1Db.permissionExpiration);
    const apiExpirationDate = new Date(user1Db.apiPermissionExpiration);

    expect(expirationDate.getTime()).toBeLessThan(after10days.getTime());
    expect(Math.floor(expirationDate.getTime() / 1000)).toBe(
      Math.floor(after2days.getTime() / 1000),
    );
    expect(apiExpirationDate.getTime()).toBeLessThan(after10days.getTime());
    expect(Math.floor(apiExpirationDate.getTime() / 1000)).toBe(
      Math.floor(after2days.getTime() / 1000),
    );

    checkWebhookSignatureMock.mockRestore();
    getProductByIdMock.mockRestore();
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
