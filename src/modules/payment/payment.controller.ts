import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  Param,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { PaymentService } from '@modules/payment/payment.service';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  CancelSubscriptionResponseDto,
  CreateSubscriptionSessionParamsDto,
  CreateSubscriptionSessionResponseDto,
  WebhookResponseDto,
} from '@modules/payment-gateway/dto/payment-gateway.dto';
import { JwtAuthGuard } from '@modules/user/jwt.guard';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_CONFLICT,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_NOT_FOUND,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { AccessStrategyDto } from '@modules/user/dto/strategy.dto';
import { ProductsResponseDto } from '@modules/payment/dto/products.dto';
import {
  PaymentCryptoAddressResponseDto,
  PaymentCryptoChains,
  PaymentCryptoContractAddressesResponseDto,
  PaymentCryptoCurrencies,
  SaveTransactionIdParams,
  SaveTransactionIdResponseDto,
} from '@modules/payment-crypto/dto/payment-crypto.dto';
import { SubscriptionDtoResponse } from '@modules/payment/dto/subscription.dto';
import { PendingTransaction } from '@root/internal-db/models/PendingTransaction';
import { StartPaymentDto } from './dto/start-payment.dto';
import { UserService } from '../user/user.service';

@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly userService: UserService,
  ) {}

  @Get('subscription')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get current subscription details.',
  })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getSubscription(
    @Request() { user }: AccessStrategyDto,
  ): Promise<SubscriptionDtoResponse> {
    return this.paymentService.getSubscription({ userId: user.id });
  }

  @Get('products')
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get available products.',
  })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiConflictResponse({ description: ERROR_HTTP_CONFLICT })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getProducts(): Promise<ProductsResponseDto> {
    return this.paymentService.getProducts();
  }

  @Get('create-subscription-session/:priceId')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Create subscription session for user (initialization).',
  })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiConflictResponse({ description: ERROR_HTTP_CONFLICT })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  signIn(
    @Param() { priceId }: CreateSubscriptionSessionParamsDto,
    @Request() { user }: AccessStrategyDto,
  ): Promise<CreateSubscriptionSessionResponseDto> {
    return this.paymentService.createSubscriptionSession({
      userId: user.id,
      priceId,
    });
  }

  @Post('cancel-subscription')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Cancel user subscription.',
  })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiNotFoundResponse({ description: ERROR_HTTP_NOT_FOUND })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  cancelSubscription(
    @Request() { user }: AccessStrategyDto,
  ): Promise<CancelSubscriptionResponseDto> {
    return this.paymentService.cancelSubscription({ userId: user.id });
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Obtain payments data from payment gateway.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  refreshTokens(
    @Body() body,
    @Headers('stripe-signature') signature: string,
  ): Promise<WebhookResponseDto> {
    return this.paymentService.webhook({ body, signature });
  }

  @Get('crypto/address')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get "pay to" address.',
  })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getCryptoAddress(): PaymentCryptoAddressResponseDto {
    return this.paymentService.getCryptoAddress();
  }

  @Get('crypto/contract-addresses')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get available contract addresses.',
  })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getCryptoContractAddresses(): PaymentCryptoContractAddressesResponseDto {
    return this.paymentService.getCryptoContractAddresses();
  }

  @Get('crypto')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({
    summary: 'Get list of user crypto transactions.',
  })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getTransactions(
    @Request() { user }: AccessStrategyDto,
  ): Promise<PendingTransaction[]> {
    return this.paymentService.getTransactions({ userId: user.id });
  }

  @Post('crypto/save-transaction-id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({ summary: 'Save transaction id to track it in blockchain.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  saveTransactionId(
    @Request() { user }: AccessStrategyDto,
    @Body() body: SaveTransactionIdParams,
  ): Promise<SaveTransactionIdResponseDto> {
    const blockchain: PaymentCryptoChains =
      body.blockchain as PaymentCryptoChains;
    const contract: PaymentCryptoCurrencies =
      body.contract as PaymentCryptoCurrencies;

    return this.paymentService.saveTransactionId({
      userId: user.id,
      ...body,
      blockchain,
      contract,
    });
  }

  @Post('/start-payment')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @HttpCode(200)
  @ApiOperation({ summary: 'Start payment.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiConflictResponse({ description: ERROR_HTTP_CONFLICT })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  startPurchase(
    @Request() req: AccessStrategyDto,
    @Body() body: StartPaymentDto,
  ) {
    return this.userService.startPayment(req.user.id, body);
  }
}
