import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import {
  CancelSubscriptionResponseDto,
  CreateSubscriptionSession,
  CreateSubscriptionSessionResponseDto,
  WebhookResponseDto,
} from '@modules/payment-gateway/dto/payment-gateway.dto';
import { Stripe } from 'stripe';
import { UserService } from '@modules/user/user.service';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_NOT_FOUND,
} from '@config/error-messages';
import {
  MappedProduct,
  ProductsResponseDto,
} from '@modules/payment/dto/products.dto';
import { PaymentCryptoService } from '@modules/payment-crypto/payment-crypto.service';
import {
  FindTransactionsByUserId,
  PaymentCryptoAddressResponseDto,
  PaymentCryptoContractAddressesResponseDto,
  SaveTransactionId,
  SaveTransactionIdResponseDto,
} from '@modules/payment-crypto/dto/payment-crypto.dto';
import { PendingTransactionRepository } from '@modules/payment-crypto/pending-transactions.repository';
import { PENDING_TRANSACTION_REPOSITORY_TOKEN } from '@modules/payment-crypto/tokens';
import {
  SubscriptionDto,
  SubscriptionDtoResponse,
} from '@modules/payment/dto/subscription.dto';
import { API_PERMISSIONS_LIST, Permissions } from '@common/utils/permissions';
import { PendingTransaction } from '@root/internal-db/models/PendingTransaction';
import { EmailService } from '../email/email.service';
import { UpdateSubscription } from '../user/dto/payer.dto';
import { ConfirmationSubscriptionParamsDto } from '../email/dto/email.dto';
import { CURRENCE_DECIMAL } from './constants';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    private readonly emailService: EmailService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly paymentCryptoService: PaymentCryptoService,
    private readonly userService: UserService,
    @Inject(PENDING_TRANSACTION_REPOSITORY_TOKEN)
    private readonly pendingTransactionRepository: PendingTransactionRepository,
  ) {}

  async getProducts(): Promise<ProductsResponseDto> {
    const sort = (a, b) =>
      parseInt(a.metadata.Order || '0') - parseInt(b.metadata.Order || '0');

    const { data: products } = await this.paymentGatewayService.getProducts();
    const { data: prices } = await this.paymentGatewayService.getPrices();
    const mappedProducts = products
      .map((product) => {
        const mappedProduct: MappedProduct = { ...product, prices: [] };

        for (const price of prices) {
          if (product.id === price.product) {
            mappedProduct.prices.push(price);
          }
        }

        return mappedProduct;
      })
      .sort(sort);

    return { products: mappedProducts };
  }

  async createSubscriptionSession({
    userId,
    priceId,
  }: CreateSubscriptionSession): Promise<CreateSubscriptionSessionResponseDto> {
    const user = await this.userService.getUserByCondition({ id: userId });
    const params = { userId, priceId };

    if (user.payerId) {
      params['payerId'] = user.payerId;
    }

    if (user.email) {
      params['userEmail'] = user.email;
    }

    return this.paymentGatewayService.createSubscriptionSession(params);
  }

  async cancelSubscription({ userId }): Promise<CancelSubscriptionResponseDto> {
    const user = await this.userService.getUserByCondition({ id: userId });

    if (!user.payerId) {
      throw new NotFoundException(ERROR_HTTP_NOT_FOUND);
    }

    const currentSubscription =
      await this.paymentGatewayService.getCurrentSubscription({
        customerId: user.payerId,
      });

    if (!currentSubscription.id) {
      throw new NotFoundException(ERROR_HTTP_NOT_FOUND);
    }

    await this.paymentGatewayService.cancelSubscription({
      id: currentSubscription.id,
    });

    return { success: true };
  }

  async webhook({ body, signature }): Promise<WebhookResponseDto> {
    this.logger.log('Start processing stripe webhook');

    const { data, type } =
      await this.paymentGatewayService.checkWebhookSignature({
        body,
        signature,
      });

    switch (type) {
      case 'checkout.session.completed':
        const id = (data.object as Stripe.Checkout.Session).client_reference_id;
        const payerId = (data.object as Stripe.Checkout.Session)
          .customer as string;

        await this.userService.assignPayerId({ id, payerId });
        break;
      case 'invoice.paid':
        const invoice = data.object as Stripe.Invoice;

        this.logger.log(`Processing of paid invoice: ${invoice.id}`);

        const invoicePayerId = invoice.customer as string;
        const invoiceLineItem = invoice?.lines?.data[0];
        const productId = invoiceLineItem?.price?.product;
        const product = await this.paymentGatewayService.getProductById({
          productId,
        });

        const paymnetPeriod = invoiceLineItem?.period;

        if (!paymnetPeriod.end) {
          this.logger.error(
            `No permission expiration for product: ${productId} (${product?.name})`,
          );
          throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
        }

        if (!product.metadata.Access || !product.metadata['API Access']) {
          this.logger.error(
            `No access configured for product: ${productId} (${product?.name})`,
          );
          throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
        }

        const permissionExpirationDate = new Date(paymnetPeriod.end * 1000);

        const subscriptionParams: UpdateSubscription = {
          payerId: invoicePayerId,
          permissions: parseInt(product.metadata.Access, 2),
          permissionExpiration: permissionExpirationDate,
          apiPermissions: parseInt(product.metadata['API Access'], 2),
          apiPermissionExpiration: new Date(paymnetPeriod.end * 1000),
        };

        this.logger.log(
          `Update subscription: ${JSON.stringify(subscriptionParams)}`,
        );

        await this.userService.updateSubscription(subscriptionParams);

        const startDate = new Date(paymnetPeriod.start * 1000);

        const currentUser = await this.userService.getUserByCondition({
          payerId: invoicePayerId,
        });

        const amount =
          invoice.lines.data.reduce((result, item) => {
            result += item.amount;

            return result;
          }, 0) / CURRENCE_DECIMAL;

        const emailParams: ConfirmationSubscriptionParamsDto = {
          plan: product?.name,
          startDate: startDate.toLocaleDateString('en-US'),
          nextBillingDate: permissionExpirationDate.toLocaleDateString('en-US'),
          subscriptionAmount: `${amount} ${invoice.lines.data[0].currency} `,
        };

        this.logger.log(
          `Send a subscription confirmation email: ${JSON.stringify({
            params: emailParams,
            to: currentUser.email,
          })}`,
        );

        await this.emailService.sendConfirmationSubscription({
          params: emailParams,
          to: currentUser.email,
        });
        this.logger.log(`Paid invoice successfully processed: ${invoice.id}`);
        break;
      case 'customer.subscription.deleted':
        const deletedPayerId = (data.object as Stripe.Subscription)
          .customer as string;

        await this.userService.updateSubscription({
          payerId: deletedPayerId,
          permissions: 0,
          permissionExpiration: null,
          apiPermissions: 0,
          apiPermissionExpiration: null,
        });
        break;
      case 'customer.subscription.updated':
        const updatedPayerId = (data.object as Stripe.Subscription)
          .customer as string;

        const cancelAt = (data.object as Stripe.Subscription).cancel_at;

        if (cancelAt) {
          await this.userService.updateSubscription({
            payerId: updatedPayerId,
            permissionExpiration: new Date(cancelAt * 1000),
            apiPermissionExpiration: new Date(cancelAt * 1000),
          });
        }

        break;
      default:
        this.logger.log(`Unhandled payment event type: ${type}`);
    }

    return { success: true };
  }

  getCryptoAddress(): PaymentCryptoAddressResponseDto {
    return this.paymentCryptoService.getPaymentAddress();
  }

  getCryptoContractAddresses(): PaymentCryptoContractAddressesResponseDto {
    return this.paymentCryptoService.getContractAddresses();
  }

  getTransactions({
    userId,
  }: FindTransactionsByUserId): Promise<PendingTransaction[]> {
    return this.paymentCryptoService.getTransactions({ userId });
  }

  async saveTransactionId(
    params: SaveTransactionId,
  ): Promise<SaveTransactionIdResponseDto> {
    await this.paymentCryptoService.saveTransactionId(params);

    return { success: true };
  }

  async getSubscription({
    userId,
  }: SubscriptionDto): Promise<SubscriptionDtoResponse> {
    const subscription: SubscriptionDtoResponse = {
      name: 'Free',
      period: null,
      paymentMethod: null,
      periodEnds: null,
      amount: null,
      apiAccess: [],
      cancelAt: null,
      cancelAtPeriodEnd: false,
    };

    const { payerId, permissionExpiration, apiPermissions } =
      await this.userService.getUserByCondition({ id: userId });
    let priceId: string;

    if (payerId) {
      const currentSubscription =
        await this.paymentGatewayService.getCurrentSubscription({
          customerId: payerId,
        });

      if (currentSubscription) {
        priceId = currentSubscription.items?.data?.[0]?.price?.id;

        if (priceId) {
          subscription.paymentMethod = 'fiat';
          subscription.cancelAt = currentSubscription.cancel_at
            ? new Date(currentSubscription.cancel_at * 1000)
            : null;
          subscription.cancelAtPeriodEnd =
            currentSubscription.cancel_at_period_end;
        }
      }
    }

    if (!priceId) {
      const lastCryptoTransaction =
        await this.pendingTransactionRepository.getLastHandledTransaction({
          userId,
        });

      if (lastCryptoTransaction) {
        priceId = lastCryptoTransaction.priceId;
        subscription.paymentMethod = 'crypto';
      }
    }

    if (priceId) {
      const plan = await this.paymentGatewayService.getPlanDetails({ priceId });

      subscription.name = (plan.product as Stripe.Product).name;
      subscription.period = plan.recurring.interval;
      subscription.amount = plan.unit_amount;
      subscription.periodEnds = permissionExpiration;

      if (
        Permissions.checkApiPermission(
          apiPermissions,
          API_PERMISSIONS_LIST.PQRS,
        )
      ) {
        subscription.apiAccess.push("PQR's");
      }

      if (
        Permissions.checkApiPermission(
          apiPermissions,
          API_PERMISSIONS_LIST.CONTRACT_SCORES,
        )
      ) {
        subscription.apiAccess.push('Contract Scores');
      }

      if (
        Permissions.checkApiPermission(
          apiPermissions,
          API_PERMISSIONS_LIST.CHAIN_SCORES,
        )
      ) {
        subscription.apiAccess.push('Chain Scores');
      }
    }

    return subscription;
  }
}
