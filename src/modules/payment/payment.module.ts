import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { PaymentGatewayModule } from '@modules/payment-gateway/payment-gateway.module';
import { UserModule } from '@modules/user/user.module';
import { PaymentCryptoModule } from '@modules/payment-crypto/payment-crypto.module';

@Module({
  imports: [PaymentGatewayModule, PaymentCryptoModule, UserModule],
  providers: [PaymentService],
  controllers: [PaymentController],
  exports: [PaymentService],
})
export class PaymentModule {}
