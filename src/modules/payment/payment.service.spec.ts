import { Test, TestingModule } from '@nestjs/testing';
import { PaymentService } from './payment.service';
import { PaymentController } from '@modules/payment/payment.controller';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { UserService } from '@modules/user/user.service';
import { Stripe } from 'stripe';
import { GetUserByCondition } from '@modules/user/dto/user.dto';
import { User } from '@root/internal-db/models/User';
import { TEST_DEFAULT_EMAIL_1 } from '@root/setup/e2e-test.setup';
import { CreateSubscriptionSessionResponseDto } from '@modules/payment-gateway/dto/payment-gateway.dto';
import { v4 as uuidv4 } from 'uuid';
import { PaymentCryptoService } from '@modules/payment-crypto/payment-crypto.service';
import { PENDING_TRANSACTION_REPOSITORY_TOKEN } from '@modules/payment-crypto/tokens';

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentGatewayService: PaymentGatewayService;
  let userService: UserService;

  const payerId = 'client_lk2398djfsdjf';
  const priceId = 'price_1Ko3noB29B3waVVFDiC4Mdae';
  const userId = uuidv4();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: PaymentGatewayService,
          useValue: {
            getProducts: () => Promise.resolve({}),
            getPrices: () => Promise.resolve({}),
            createSubscriptionSession: () => Promise.resolve({}),
            checkWebhookSignature: () => Promise.resolve({}),
          },
        },
        {
          provide: UserService,
          useValue: {
            getUserByCondition: () => Promise.resolve({}),
          },
        },
        {
          provide: PaymentCryptoService,
          useValue: {
            getPaymentAddress: () => Promise.resolve({}),
          },
        },
        {
          provide: PENDING_TRANSACTION_REPOSITORY_TOKEN,
          useValue: {},
        },
      ],
      controllers: [PaymentController],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    paymentGatewayService = module.get<PaymentGatewayService>(
      PaymentGatewayService,
    );
    userService = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return the list of products, ordered and mapped with prices', async () => {
    const products: Stripe.Product[] = [
      {
        id: 'prod_LPSI8XVkJWbl8l',
        object: 'product',
        active: true,
        attributes: [],
        caption: null,
        created: 1648553902,
        description: '* Contract Scores on web \\n * Chain Scores on web',
        images: [],
        livemode: false,
        metadata: {
          'Analyst Support': 'Private Discord Support Channel',
          'Private Reports': 'Quoted Separately',
          Order: '6',
          Access: '100',
          'API Access': '000',
        },
        name: 'Low',
        package_dimensions: null,
        shippable: null,
        statement_descriptor: null,
        tax_code: null,
        type: 'service',
        unit_label: null,
        updated: 1649852506,
        url: null,
      },
      {
        id: 'prod_LV3lnJfm3ylDKL',
        object: 'product',
        active: true,
        attributes: [],
        caption: null,
        created: 1649846507,
        description: null,
        images: [],
        livemode: false,
        metadata: {
          Order: '4',
          Access: '100',
          'API Access': '000',
        },
        name: 'API access to Chain Scores',
        package_dimensions: null,
        shippable: null,
        statement_descriptor: null,
        tax_code: null,
        type: 'service',
        unit_label: null,
        updated: 1649852645,
        url: null,
      },
      {
        id: 'prod_LPSmzIX4atHcNz',
        object: 'product',
        active: true,
        attributes: [],
        caption: null,
        created: 1648555716,
        description:
          "* Contract Scores on web \\n * Chain Scores on web \\n * PQR's with Chain Scores for web",
        images: [],
        livemode: false,
        metadata: {
          'Analyst Support': 'Up to 2 hours per month',
          'Private Reports': 'One per quarter',
          Type: 'Plan',
          Order: '3',
          Access: '111',
          'API Access': '010',
        },
        name: 'High',
        package_dimensions: null,
        shippable: null,
        statement_descriptor: null,
        tax_code: null,
        type: 'service',
        unit_label: null,
        updated: 1649852557,
        url: null,
      },
    ];

    const prices: Stripe.Price[] = [
      {
        id: 'price_1Ko3noB29B3waVVFDiC4MdmS',
        object: 'price',
        active: true,
        billing_scheme: 'per_unit',
        created: **********,
        currency: 'usd',
        livemode: false,
        lookup_key: null,
        metadata: {},
        nickname: null,
        product: 'prod_LV3lnJfm3ylDKL',
        recurring: {
          aggregate_usage: null,
          interval: 'month',
          interval_count: 1,
          trial_period_days: null,
          usage_type: 'licensed',
        },
        tax_behavior: 'unspecified',
        tiers_mode: null,
        transform_quantity: null,
        type: 'recurring',
        unit_amount: 130000,
        unit_amount_decimal: '130000',
      },
      {
        id: 'price_1Ko3noB29B3waVVFfpyFQRCv',
        object: 'price',
        active: true,
        billing_scheme: 'per_unit',
        created: **********,
        currency: 'usd',
        livemode: false,
        lookup_key: null,
        metadata: {},
        nickname: null,
        product: 'prod_LV3lnJfm3ylDKL',
        recurring: {
          aggregate_usage: null,
          interval: 'year',
          interval_count: 1,
          trial_period_days: null,
          usage_type: 'licensed',
        },
        tax_behavior: 'unspecified',
        tiers_mode: null,
        transform_quantity: null,
        type: 'recurring',
        unit_amount: 1560000,
        unit_amount_decimal: '1560000',
      },
    ];

    const paymentGatewayProductsMock = jest
      .spyOn(paymentGatewayService, 'getProducts')
      .mockImplementation(() => Promise.resolve({ data: products }) as any);
    const paymentGatewayPricesMock = jest
      .spyOn(paymentGatewayService, 'getPrices')
      .mockImplementation(() => Promise.resolve({ data: prices }) as any);

    const res = await service.getProducts();

    expect(res).toHaveProperty('products');
    expect(res.products.length).toBe(3);
    expect(res.products[0]?.metadata?.Order).toBe('3');
    expect(res.products[0].prices?.length).toBe(0);
    expect(res.products[1]?.metadata?.Order).toBe('4');
    expect(res.products[1].prices?.length).toBe(2);

    paymentGatewayProductsMock.mockRestore();
    paymentGatewayPricesMock.mockRestore();
  });

  it('should create subscription session and enrich by user data', async () => {
    const getUserByConditionMock = jest
      .spyOn(userService, 'getUserByCondition')
      .mockImplementation((params: GetUserByCondition) => {
        expect(params.id).toBe(userId);

        return Promise.resolve({
          payerId,
          email: TEST_DEFAULT_EMAIL_1,
        } as User);
      });

    const createSubscriptionSessionMock = jest
      .spyOn(paymentGatewayService, 'createSubscriptionSession')
      .mockImplementation((params) => {
        expect(params?.userId).toBe(userId);
        expect(params?.priceId).toBe(priceId);
        expect(params?.payerId).toBe(payerId);
        expect(params?.userEmail).toBe(TEST_DEFAULT_EMAIL_1);

        return Promise.resolve({} as CreateSubscriptionSessionResponseDto);
      });

    await service.createSubscriptionSession({ userId, priceId });
    expect(getUserByConditionMock.mock.calls.length).toBe(1);
    expect(createSubscriptionSessionMock.mock.calls.length).toBe(1);

    getUserByConditionMock.mockRestore();
    createSubscriptionSessionMock.mockRestore();
  });

  it('should create subscription session without additional data', async () => {
    const getUserByConditionMock = jest
      .spyOn(userService, 'getUserByCondition')
      .mockImplementation((params: GetUserByCondition) => {
        expect(params.id).toBe(userId);

        return Promise.resolve({} as User);
      });

    const createSubscriptionSessionMock = jest
      .spyOn(paymentGatewayService, 'createSubscriptionSession')
      .mockImplementation((params) => {
        expect(params?.userId).toBe(userId);
        expect(params?.priceId).toBe(priceId);
        expect(params).not.toHaveProperty('payerId');
        expect(params).not.toHaveProperty('userEmail');

        return Promise.resolve({} as CreateSubscriptionSessionResponseDto);
      });

    await service.createSubscriptionSession({ userId, priceId });
    expect(getUserByConditionMock.mock.calls.length).toBe(1);
    expect(createSubscriptionSessionMock.mock.calls.length).toBe(1);

    getUserByConditionMock.mockRestore();
    createSubscriptionSessionMock.mockRestore();
  });
});
