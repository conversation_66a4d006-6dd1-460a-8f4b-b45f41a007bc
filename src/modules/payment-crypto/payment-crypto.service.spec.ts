import { Test, TestingModule } from '@nestjs/testing';
import { PaymentCryptoService } from './payment-crypto.service';
import { HttpService } from '@nestjs/axios';
import {
  PAYMENT_CRYPTO_OPTIONS,
  PENDING_TRANSACTION_REPOSITORY_TOKEN,
} from '@modules/payment-crypto/tokens';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { UserService } from '@modules/user/user.service';

describe('PaymentCryptoService', () => {
  let service: PaymentCryptoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentCryptoService,
        {
          provide: PENDING_TRANSACTION_REPOSITORY_TOKEN,
          useValue: {},
        },
        {
          provide: PAYMENT_CRYPTO_OPTIONS,
          useValue: {},
        },
        {
          provide: HttpService,
          useValue: {
            get: () => Promise.resolve({}),
          },
        },
        {
          provide: PaymentGatewayService,
          useValue: {
            get: () => Promise.resolve({}),
          },
        },
        {
          provide: UserService,
          useValue: {
            get: () => Promise.resolve({}),
          },
        },
      ],
    }).compile();

    service = module.get<PaymentCryptoService>(PaymentCryptoService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
