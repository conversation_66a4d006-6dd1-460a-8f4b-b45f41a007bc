import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  PAYMENT_CRYPTO_OPTIONS,
  PENDING_TRANSACTION_REPOSITORY_TOKEN,
} from '@modules/payment-crypto/tokens';
import {
  FindTransactionsByUserId,
  PaymentCryptoAddresses,
  PaymentCryptoAddressResponseDto,
  PaymentCryptoOptions,
  PaymentCryptoProviders,
  SaveTransactionId,
} from '@modules/payment-crypto/dto/payment-crypto.dto';
import { Cron } from '@nestjs/schedule';
import Web3 from 'web3';
import { HttpService } from '@nestjs/axios';
import InputDataDecoder from 'ethereum-input-data-decoder';
import { PendingTransactionRepository } from '@modules/payment-crypto/pending-transactions.repository';
import { PendingTransaction } from '@root/internal-db/models/PendingTransaction';
import { ABI } from '@modules/payment-crypto/abi';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { UserService } from '@modules/user/user.service';
import { EmailService } from '../email/email.service';
import { ConfirmationSubscriptionParamsDto } from '../email/dto/email.dto';

@Injectable()
export class PaymentCryptoService {
  private readonly logger = new Logger(PaymentCryptoService.name);

  public web3 = new Web3();
  private readonly payTo: string;
  private readonly addresses: PaymentCryptoAddresses;
  private readonly providers: PaymentCryptoProviders;

  constructor(
    private readonly httpService: HttpService,
    private readonly emailService: EmailService,
    private readonly paymentGatewayService: PaymentGatewayService,
    private readonly userService: UserService,
    @Inject(PENDING_TRANSACTION_REPOSITORY_TOKEN)
    private readonly pendingTransactionRepository: PendingTransactionRepository,
    @Inject(PAYMENT_CRYPTO_OPTIONS)
    private readonly paymentCryptoOptions: PaymentCryptoOptions,
  ) {
    this.payTo = this.web3.utils.toChecksumAddress(
      paymentCryptoOptions.CRYPTO_DEFISAFETY_CONTRACT_ADDRESS,
    );
    this.addresses = {
      eth: {
        usdt: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ETH_USDT_ADDRESS,
          ),
          decimals: 6,
        },
        usdc: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ETH_USDC_ADDRESS,
          ),
          decimals: 6,
        },
        dai: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ETH_DAI_ADDRESS,
          ),
          decimals: 18,
        },
      },
      arb: {
        usdt: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ARB_USDT_ADDRESS,
          ),
          decimals: 6,
        },
        usdc: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ARB_USDC_ADDRESS,
          ),
          decimals: 6,
        },
        dai: {
          address: this.web3.utils.toChecksumAddress(
            paymentCryptoOptions.CRYPTO_ARB_DAI_ADDRESS,
          ),
          decimals: 18,
        },
      },
    };
    this.providers = {
      eth: new Web3.providers.HttpProvider(
        paymentCryptoOptions.CRYPTO_ETH_PROVIDER,
      ),
      arb: new Web3.providers.HttpProvider(
        paymentCryptoOptions.CRYPTO_ARB_PROVIDER,
      ),
    };
  }

  public getPaymentAddress(): PaymentCryptoAddressResponseDto {
    return { paymentAddress: this.payTo };
  }

  getContractAddresses(): PaymentCryptoAddresses {
    return this.addresses;
  }

  getTransactions({
    userId,
  }: FindTransactionsByUserId): Promise<PendingTransaction[]> {
    return this.pendingTransactionRepository.findByUserId({ userId });
  }

  saveTransactionId(params: SaveTransactionId): Promise<PendingTransaction> {
    return this.pendingTransactionRepository.create(params);
  }

  @Cron('0 */2 * * * *')
  async processPendingTransactions() {
    const decoder = new InputDataDecoder(ABI);

    const before1Day = new Date(Date.now() - **********);
    const pendingTransactions =
      await this.pendingTransactionRepository.getPendingTransactions({
        dateAfter: before1Day,
      });

    if (!pendingTransactions.length) {
      return null;
    }

    const { data: products } = await this.paymentGatewayService.getProducts();
    const { data: prices } = await this.paymentGatewayService.getPrices();

    for (const pendingTransaction of pendingTransactions) {
      if (!this.providers[pendingTransaction.blockchain]) {
        this.logger.error(
          'Wrong blockchain in database of provider not configured: ' +
            pendingTransaction.blockchain,
        );
        continue;
      }

      this.web3.setProvider(this.providers[pendingTransaction.blockchain]);

      const unhandledTransactionsRes = await this.web3.eth.getTransaction(
        pendingTransaction.transactionId,
      );

      if (!unhandledTransactionsRes) {
        this.logger.error(
          'Transaction ID is not valid: ' + pendingTransaction.transactionId,
        );
        continue;
      }

      const { from, to, blockNumber, input } = unhandledTransactionsRes;

      const currentBlockNumber = await this.web3.eth.getBlockNumber();

      if (currentBlockNumber - blockNumber < 10) {
        // Skip for this run, because not enough confirmations yet
        continue;
      }

      const price = prices.find(
        (price) => price.id === pendingTransaction.priceId,
      );

      if (!price?.product || !price.unit_amount) {
        this.logger.error(
          'No product exist for price or no price configured: ' + price?.id,
        );
        continue;
      }

      const product = products.find((product) => product.id === price.product);

      if (!product) {
        this.logger.error('No product exist: ' + price.product);
        continue;
      }

      const user = await this.userService.getUserByCondition({
        id: pendingTransaction.userId,
        publicAddress: from,
      });

      if (!user) {
        this.logger.error('No user exist: ' + pendingTransaction.userId);
        continue;
      }

      const { address, decimals } =
        this.addresses[pendingTransaction.blockchain]?.[
          pendingTransaction.contract
        ];

      if (to !== address) {
        this.logger.error('Contract address is incorrect: ' + to);
        continue;
      }

      const result = decoder.decodeData(input);

      if (result.method !== 'transfer') {
        this.logger.error('Method is incorrect: ' + result.method);
        continue;
      }

      if (result.inputs[0] !== this.payTo.substring(2)) {
        this.logger.error('Receiver address is incorrect: ' + result.inputs[0]);
        continue;
      }

      const amount = Math.round(
        (this.web3.utils.hexToNumber(result.inputs[1]) as number) /
          Math.pow(10, decimals),
      );
      const priceDecimal = Math.round(price.unit_amount / 100);

      if (amount !== priceDecimal) {
        this.logger.error(
          'Amount is incorrect for transaction: ' +
            pendingTransaction.transactionId,
        );
        continue;
      }

      const expiration = new Date(pendingTransaction.updatedAt);

      if (price?.recurring?.interval === 'year') {
        expiration.setFullYear(expiration.getFullYear() + 1);
      } else if (price?.recurring?.interval === 'month') {
        expiration.setMonth(expiration.getMonth() + 1);
      } else {
        this.logger.error(
          'Unconfigured recurring interval for price: ' + price.id,
        );
        continue;
      }

      const subscriptionResponse = await this.userService.updateSubscription({
        id: user.id,
        permissions: parseInt(product.metadata.Access, 2),
        permissionExpiration: expiration,
        apiPermissions: parseInt(product.metadata['API Access'], 2),
        apiPermissionExpiration: expiration,
      });

      if (subscriptionResponse.success) {
        this.logger.log(
          'Payment successful: ' + pendingTransaction.transactionId,
        );

        await this.pendingTransactionRepository.markProcessed({
          id: pendingTransaction.id,
        });

        const startDate = new Date(pendingTransaction.updatedAt);

        const emailParams: ConfirmationSubscriptionParamsDto = {
          plan: product.name,
          startDate: startDate.toLocaleDateString('en-US'),
          nextBillingDate: expiration.toLocaleDateString('en-US'),
          subscriptionAmount: `${amount} ${price.currency}`,
        };

        this.logger.log(
          `Send a subscription confirmation email: ${JSON.stringify({
            params: emailParams,
            to: user.email,
          })}`,
        );

        await this.emailService.sendConfirmationSubscription({
          params: emailParams,
          to: user.email,
        });
      } else {
        this.logger.error(
          'Payment unsuccessful: ' + pendingTransaction.transactionId,
        );
      }
    }
  }
}
