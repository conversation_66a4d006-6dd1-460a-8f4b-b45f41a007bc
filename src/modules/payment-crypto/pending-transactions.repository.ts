import { Injectable } from '@nestjs/common';
import {
  DeleteResult,
  FindConditions,
  MoreThanOrEqual,
  Repository,
  UpdateResult,
} from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { PendingTransaction } from '@root/internal-db/models/PendingTransaction';
import {
  FindTransactionsByUserId,
  SaveTransactionId,
} from '@modules/payment-crypto/dto/payment-crypto.dto';
import { OrderDto } from '@common/dto/order.dto';

@Injectable()
export class PendingTransactionRepository {
  constructor(
    @InjectRepository(PendingTransaction, INTERNAL_DB_CONNECTION)
    private readonly pendingTransactionsRepository: Repository<PendingTransaction>,
  ) {}

  getPendingTransactions({
    dateAfter,
  }: {
    dateAfter: Date;
  }): Promise<PendingTransaction[]> {
    return this.pendingTransactionsRepository.find({
      updatedAt: MoreThanOrEqual(dateAfter),
      isProcessed: false,
    });
  }

  getLastHandledTransaction({ userId }): Promise<PendingTransaction | null> {
    return this.pendingTransactionsRepository.findOne(
      {
        userId,
        isProcessed: true,
      },
      {
        order: {
          updatedAt: OrderDto.DESC,
        },
      },
    );
  }

  findByUserId({
    userId,
  }: FindTransactionsByUserId): Promise<PendingTransaction[]> {
    return this.pendingTransactionsRepository.find({ userId });
  }

  findOne(conditions: FindConditions<PendingTransaction>) {
    return this.pendingTransactionsRepository.findOne(conditions);
  }

  async create({
    userId,
    transactionId,
    priceId,
    blockchain,
    contract,
  }: SaveTransactionId): Promise<PendingTransaction> {
    const transaction = await this.pendingTransactionsRepository.create({
      userId,
      transactionId,
      priceId,
      blockchain,
      contract,
    });

    return this.pendingTransactionsRepository.save(transaction);
  }

  markProcessed({ id }): Promise<UpdateResult> {
    return this.pendingTransactionsRepository.update(
      { id },
      { isProcessed: true },
    );
  }

  delete({ id }): Promise<DeleteResult> {
    return this.pendingTransactionsRepository.delete({ id });
  }
}
