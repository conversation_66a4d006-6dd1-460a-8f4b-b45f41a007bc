import { DynamicModule, Global, Module, ValueProvider } from '@nestjs/common';
import { PaymentCryptoService } from './payment-crypto.service';
import {
  PAYMENT_CRYPTO_OPTIONS,
  PENDING_TRANSACTION_REPOSITORY_TOKEN,
} from '@modules/payment-crypto/tokens';
import { PaymentCryptoOptions } from '@modules/payment-crypto/dto/payment-crypto.dto';
import { HttpModule } from '@nestjs/axios';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PendingTransaction } from '@root/internal-db/models/PendingTransaction';
import { PendingTransactionRepository } from '@modules/payment-crypto/pending-transactions.repository';
import { PaymentGatewayModule } from '@modules/payment-gateway/payment-gateway.module';
import { UserModule } from '@modules/user/user.module';

@Global()
@Module({})
export class PaymentCryptoModule {
  public static forRoot(options: PaymentCryptoOptions): DynamicModule {
    const PaymentCryptoOptionsProvider: ValueProvider<PaymentCryptoOptions> = {
      provide: PAYMENT_CRYPTO_OPTIONS,
      useValue: options,
    };

    return {
      module: PaymentCryptoModule,
      imports: [
        HttpModule,
        TypeOrmModule.forFeature([PendingTransaction], INTERNAL_DB_CONNECTION),
        PaymentGatewayModule,
        UserModule,
      ],
      providers: [
        PaymentCryptoService,
        PaymentCryptoOptionsProvider,
        {
          provide: PENDING_TRANSACTION_REPOSITORY_TOKEN,
          useClass: PendingTransactionRepository,
        },
      ],
      exports: [PaymentCryptoService, PENDING_TRANSACTION_REPOSITORY_TOKEN],
    };
  }
}
