import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export interface PaymentCryptoOptions {
  CRYPTO_DEFISAFETY_CONTRACT_ADDRESS: string;
  CRYPTO_ETH_USDT_ADDRESS: string;
  CRYPTO_ETH_USDC_ADDRESS: string;
  CRYPTO_ETH_DAI_ADDRESS: string;
  CRYPTO_ARB_USDT_ADDRESS: string;
  CRYPTO_ARB_USDC_ADDRESS: string;
  CRYPTO_ARB_DAI_ADDRESS: string;
  CRYPTO_ETH_PROVIDER: string;
  CRYPTO_ARB_PROVIDER: string;
}

export type PaymentCryptoChains = 'eth' | 'arb';
export type PaymentCryptoCurrencies = 'usdt' | 'usdc' | 'dai';

export type PaymentCryptoAddresses = {
  [chain in PaymentCryptoChains]: {
    [currency in PaymentCryptoCurrencies]: {
      address: string;
      decimals: number;
    };
  };
};

export type PaymentCryptoProviders = {
  [chain in PaymentCryptoChains]: any; //TODO: type for HttpProvider?
};

export class PaymentCryptoAddressResponseDto {
  @ApiProperty({
    example: '******************************************',
  })
  paymentAddress: string;
}

export class PaymentCryptoContractAddressesResponseDto
  implements PaymentCryptoAddresses
{
  @ApiProperty()
  eth: {
    usdt: {
      address: string;
      decimals: number;
    };
    usdc: {
      address: string;
      decimals: number;
    };
    dai: {
      address: string;
      decimals: number;
    };
  };
  @ApiProperty()
  arb: {
    usdt: {
      address: string;
      decimals: number;
    };
    usdc: {
      address: string;
      decimals: number;
    };
    dai: {
      address: string;
      decimals: number;
    };
  };
}

export interface SaveTransactionId {
  userId: string;
  priceId: string;
  transactionId: string;
  blockchain: PaymentCryptoChains;
  contract: PaymentCryptoCurrencies;
}

export class SaveTransactionIdParams {
  @ApiProperty({
    description: 'Price id from stripe.',
  })
  @IsString()
  priceId: string;

  @ApiProperty({
    description: 'Transaction id from transfer() callback.',
  })
  @IsString()
  transactionId: string;

  @ApiProperty({
    description: 'One of the values: "eth" or "arb"',
    example: 'eth',
  })
  blockchain: string;

  @ApiProperty({
    description: 'One of the values: "usdt" or "usdc" or "dai"',
    default: 'usdt',
  })
  contract: string;
}

export class SaveTransactionIdResponseDto {
  @ApiProperty()
  success: boolean;
}

export interface FindTransactionsByUserId {
  userId: string;
}
