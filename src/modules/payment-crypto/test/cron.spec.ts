import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { User } from '@root/internal-db/models/User';
import { PaymentGatewayService } from '@modules/payment-gateway/payment-gateway.service';
import { PaymentCryptoService } from '@modules/payment-crypto/payment-crypto.service';
import { PendingTransactionRepository } from '@modules/payment-crypto/pending-transactions.repository';
import { PENDING_TRANSACTION_REPOSITORY_TOKEN } from '@modules/payment-crypto/tokens';

describe('Cron service for handling pending payments', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userRepository: UserRepository;
  let user1: User;
  let user2: User;
  let paymentGatewayService: PaymentGatewayService;
  let paymentCryptoService: PaymentCryptoService;
  let pendingTransactionRepository: PendingTransactionRepository;

  let productsMock: any;
  let pricesMock: any;

  const user1PublicAddress = '******************************************';
  const user2PublicAddress = '******************************************';
  const product = 'prod_LPSb0S9O9qlbZm';
  const binaryPermissions = '011';
  const binaryApiPermissions = '100';
  const price1 = 'price_1KuaIzB29B3waVVFdPYfhV51';
  const transactionId1 =
    '0xe301bb70bcb4210befda9aa3b50ce82240f6fc4edc3c4b105b1db9fc39fca3e8';
  const addresses = {
    CRYPTO_DEFISAFETY_CONTRACT_ADDRESS:
      '******************************************',
    CRYPTO_ETH_USDT_ADDRESS: '******************************************',
    CRYPTO_ETH_USDC_ADDRESS: '******************************************',
    CRYPTO_ETH_DAI_ADDRESS: '******************************************',
    CRYPTO_ARB_USDT_ADDRESS: '******************************************',
    CRYPTO_ARB_USDC_ADDRESS: '******************************************',
    CRYPTO_ARB_DAI_ADDRESS: '******************************************',
    CRYPTO_ETH_PROVIDER: '',
    CRYPTO_ARB_PROVIDER: '',
  };
  const currentBlockNumber = 123456;
  const date = new Date();
  let web3ParamsCorrectInput: string;
  let web3ParamsWrongAddressInput: string;
  let web3ParamsWrongAmountInput: string;
  let transactionInBlockchain;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    paymentGatewayService = module.get<PaymentGatewayService>(
      PaymentGatewayService,
    );
    paymentCryptoService =
      module.get<PaymentCryptoService>(PaymentCryptoService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    pendingTransactionRepository = module.get(
      PENDING_TRANSACTION_REPOSITORY_TOKEN,
    );

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    await userRepository.updatePublicAddress({
      id: user1.id,
      publicAddress: user1PublicAddress,
    });

    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    await userRepository.updatePublicAddress({
      id: user2.id,
      publicAddress: user2PublicAddress,
    });

    const productData = {
      id: product,
      object: 'product',
      active: true,
      attributes: [],
      created: 1648555053,
      description: '',
      images: [],
      livemode: false,
      metadata: {
        'Analyst Support':
          'Private API support discord Channel; Up to 2 hours per quarter',
        'Private Reports': 'Quoted separately',
        Order: '2',
        Access: binaryPermissions,
        'API Access': binaryApiPermissions,
      },
      name: 'Medium',
      package_dimensions: null,
      shippable: null,
      statement_descriptor: null,
      tax_code: null,
      type: 'service',
      unit_label: null,
      updated: 1649852531,
      url: null,
    };

    productsMock = jest
      .spyOn(paymentGatewayService, 'getProducts')
      .mockImplementation(() => ({ data: [productData] } as any));

    const priceData = {
      id: price1,
      product: product,
      unit_amount: 40000,
      recurring: {
        interval: 'year',
      },
    };

    pricesMock = jest
      .spyOn(paymentGatewayService, 'getPrices')
      .mockImplementation(() => ({ data: [priceData] } as any));

    web3ParamsCorrectInput = paymentCryptoService.web3.eth.abi.encodeParameters(
      ['address', 'uint256'],
      ['******************************************', '*********'],
    );
    web3ParamsWrongAddressInput =
      paymentCryptoService.web3.eth.abi.encodeParameters(
        ['address', 'uint256'],
        ['******************************************', '*********'],
      );
    web3ParamsWrongAmountInput =
      paymentCryptoService.web3.eth.abi.encodeParameters(
        ['address', 'uint256'],
        ['******************************************', '5000000'],
      );
    //'0xa9059cbb000000000000000000000000dedca49f044687f80d0a1ca718592224c2c6961700000000000000000000000000000000000000000000000000000000004c4b40'

    transactionInBlockchain = {
      hash: transactionId1,
      accessList: [],
      blockHash:
        '0x35f61f99f14130bfe3e4968b385c561be63034124aac744f8468df1af2f6d526',
      blockNumber: currentBlockNumber - 20,
      chainId: '0x4',
      from: user1PublicAddress,
      gas: 100000,
      gasPrice: '7491735503',
      input: '0xa9059cbb' + web3ParamsCorrectInput.substring(2),
      maxFeePerGas: '12500843412',
      maxPriorityFeePerGas: '2500000000',
      nonce: 12,
      r: '0x964ddac375415ce8bf7f06463dcd083ba0430c232db21e68c0cd850316041347',
      s: '0x6a8efd9a832b877153083697c943c8e810065fcfd316d2417d35dcde1701d671',
      to: addresses.CRYPTO_ETH_USDT_ADDRESS,
      transactionIndex: 7,
      type: 2,
      v: '0x1',
      value: '0',
    };

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    paymentCryptoService.web3.eth.getBlockNumber = () =>
      Promise.resolve(currentBlockNumber) as any;
  });

  it('should handle payment successfully', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(parseInt(binaryPermissions, 2));
    expect(user.apiPermissions).toBe(parseInt(binaryApiPermissions, 2));
    expect(new Date(user.permissionExpiration).getTime()).toBeGreaterThan(
      date.getTime(),
    );

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(true);

    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
    await userRepository.updateSubscription({
      updateBy: { id: user.id },
      fieldsToUpdate: {
        permissions: 0,
        permissionExpiration: null,
        apiPermissions: 0,
        apiPermissionExpiration: null,
      },
    });
  });

  it('should skip processing, no transactions in queue', async () => {
    const res = await paymentCryptoService.processPendingTransactions();

    expect(res).toBeNull();
  });

  it('should skip processing, unsupported blockchain', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'etc' as any,
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  it('should skip processing, not found transaction in blockchain', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () => Promise.resolve(null);

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  it('should skip processing, summ is incorrect', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve({
        ...transactionInBlockchain,
        input: '0xa9059cbb' + web3ParamsWrongAmountInput.substring(2),
      }) as any;

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  it('should skip processing, address is incorrect', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve({
        ...transactionInBlockchain,
        input: '0xa9059cbb' + web3ParamsWrongAddressInput.substring(2),
      }) as any;

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  it('should skip processing, not enough confirmations', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve({
        ...transactionInBlockchain,
        blockNumber: currentBlockNumber - 2,
      }) as any;

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  it('should skip processing, wrong address contract', async () => {
    const pendingTransaction = await pendingTransactionRepository.create({
      userId: user1.id,
      transactionId: transactionId1,
      priceId: price1,
      blockchain: 'eth',
      contract: 'usdt',
    });

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve({
        ...transactionInBlockchain,
        to: addresses.CRYPTO_ETH_DAI_ADDRESS,
      }) as any;

    await paymentCryptoService.processPendingTransactions();

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.permissions).toBe(0);

    const transaction = await pendingTransactionRepository.findOne({
      id: pendingTransaction.id,
    });

    expect(transaction.isProcessed).toBe(false);

    paymentCryptoService.web3.eth.getTransaction = () =>
      Promise.resolve(transactionInBlockchain) as any;
    await pendingTransactionRepository.delete({ id: pendingTransaction.id });
  });

  afterAll(async () => {
    productsMock.mockRestore();
    pricesMock.mockRestore();

    await app.close();
    await schema.deleteSchema();
  });
});
