import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { UserService } from '@modules/user/user.service';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';

describe('Refresh API permissions token flow', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let userRepository: UserRepository;
  let user1Tokens: SignInResponseDto;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userService = module.get<UserService>(UserService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);

    await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    user1Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });

    await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
  });

  it('should create and then refresh token successfully', async () => {
    const res1 = await supertest
      .agent(app.getHttpServer())
      .post('/user/refresh-api-token')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(res1.status).toBe(200);
    expect(res1.body).toHaveProperty('apiPermissionToken');
    expect(res1.body.apiPermissionToken.length).toBe(64);

    const user1token1 = await userRepository.findOne({
      email: TEST_DEFAULT_EMAIL_1,
    });

    expect(res1.body.apiPermissionToken).toBe(user1token1.apiPermissionToken);

    const res2 = await supertest
      .agent(app.getHttpServer())
      .post('/user/refresh-api-token')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(res2.status).toBe(200);
    expect(res2.body).toHaveProperty('apiPermissionToken');
    expect(res2.body.apiPermissionToken.length).toBe(64);

    const user1token2 = await userRepository.findOne({
      email: TEST_DEFAULT_EMAIL_1,
    });

    expect(res2.body.apiPermissionToken).toBe(user1token2.apiPermissionToken);
  });

  it('should throw an error (not valid token)', async () => {
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/refresh-api-token')
      .auth(user1Tokens.refreshToken, { type: 'bearer' })
      .send();

    expect(status).toBe(401);
    expect(body.message).toBe(ERROR_HTTP_UNAUTHORIZED);
  });

  it('should throw an error (failed to save token)', async () => {
    const refreshApiTokenMock = jest
      .spyOn(userRepository, 'refreshApiToken')
      .mockImplementation(() => Promise.resolve({ affected: 0 } as any));
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/refresh-api-token')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(500);
    expect(body.message).toBe(ERROR_HTTP_INTERNAL_SERVER_ERROR);

    refreshApiTokenMock.mockRestore();
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
