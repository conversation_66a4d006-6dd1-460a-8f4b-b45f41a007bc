import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import * as crypto from 'crypto';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_VALIDATION_EMAIL,
  ERROR_VALIDATION_PASSWORD,
  ERROR_VALIDATION_USER_EXISTS,
} from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { EmailService } from '@modules/email/email.service';
import {
  ConfirmationEmailParamsDto,
  EmailParamsDto,
} from '@modules/email/dto/email.dto';
import { User } from '@root/internal-db/models/User';

describe('Sign-up flow', () => {
  let app: INestApplication;
  let schema: RandomSchema;
  let module: TestingModule;
  let userRepository: UserRepository;
  let emailService: EmailService;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userRepository = module.get<UserRepository>(USER_REPOSITORY_TOKEN);
    emailService = module.get<EmailService>(EmailService);
  });

  it('should sign-up successfully, send verification email and retrieve correct user', async () => {
    const emailMock = jest
      .spyOn(emailService, 'sendConfirmationEmail')
      .mockImplementation(
        (params: EmailParamsDto<ConfirmationEmailParamsDto>) => {
          expect(params.params.name).toBe(TEST_DEFAULT_EMAIL_1);
          expect(params.params.token.length).toBe(64);
          expect(params.to).toBe(TEST_DEFAULT_EMAIL_1);

          return Promise.resolve();
        },
      );
    const userRes = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-up')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD,
      });

    expect(userRes.status).toBe(200);
    expect(userRes.body).toHaveProperty('accessToken');
    expect(userRes.body).toHaveProperty('refreshToken');
    expect(emailMock.mock.calls.length).toBe(1);

    const user = await supertest
      .agent(app.getHttpServer())
      .get('/user')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(userRes.body.accessToken, { type: 'bearer' })
      .send();

    expect(user.status).toBe(200);
    expect(user.body.emailConfirmed).toBe(false);
    expect(user.body).not.toHaveProperty('password');
    expect(user.body).not.toHaveProperty('salt');
    expect(user.body).not.toHaveProperty('emailConfirmationToken');
    expect(user.body).not.toHaveProperty('passwordRefreshToken');

    const userDb: User = await userRepository.findOne({
      email: TEST_DEFAULT_EMAIL_1,
    });
    const confirmationTokenInvalid = crypto.randomBytes(32).toString('hex');

    const verificationInvalidToken = await supertest
      .agent(app.getHttpServer())
      .get(`/user/confirm-email/${confirmationTokenInvalid}`)
      .send();

    expect(verificationInvalidToken.status).toBe(400);
    expect(verificationInvalidToken.body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    const verificationSuccess = await supertest
      .agent(app.getHttpServer())
      .get(`/user/confirm-email/${userDb.emailConfirmationToken}`)
      .send();

    expect(verificationSuccess.status).toBe(200);
    expect(verificationSuccess.body.success).toBe(true);

    const userVerified = await supertest
      .agent(app.getHttpServer())
      .get('/user')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(userRes.body.accessToken, { type: 'bearer' })
      .send();

    expect(userVerified.status).toBe(200);
    expect(userVerified.body.emailConfirmed).toBe(true);

    const verificationDuplicate = await supertest
      .agent(app.getHttpServer())
      .get(`/user/confirm-email/${userDb.emailConfirmationToken}`)
      .send();

    expect(verificationDuplicate.status).toBe(400);
    expect(verificationDuplicate.body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    emailMock.mockRestore();
  });

  it('should throw an error (email already registered)', async () => {
    const emailConfirmationToken = crypto.randomBytes(32).toString('hex');

    await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-up')
      .send({
        email: TEST_DEFAULT_EMAIL_2,
        password: TEST_DEFAULT_PASSWORD,
      });

    expect(status).toBe(422);
    expect(body.message).toBe(ERROR_VALIDATION_USER_EXISTS);
  });

  it('should throw an error (invalid credentials)', async () => {
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-up')
      .send({
        email: 1111,
        password: 'small',
      });

    expect(status).toBe(400);
    expect(body.message).toContain(ERROR_VALIDATION_EMAIL);
    expect(body.message).toContain(ERROR_VALIDATION_PASSWORD);
  });

  //TODO: test flow when email service fails to send email

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
