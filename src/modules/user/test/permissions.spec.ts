import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { UserService } from '@modules/user/user.service';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';
import { User } from '@root/internal-db/models/User';
import {
  ERROR_HTTP_FORBIDDEN,
  ERROR_HTTP_UNAUTHORIZED,
} from '@config/error-messages';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import { PqrService } from '@modules/pqr/pqr.service';
import { ChainScoreService } from '@modules/chain-score/chain-score.service';

describe('Permissions', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let contractScoreService: ContractScoreService;
  let pqrService: PqrService;
  let chainScoreService: ChainScoreService;
  let userRepository: UserRepository;

  let user1: User;
  let user2: User;
  let user1Tokens: SignInResponseDto;
  let user2Tokens: SignInResponseDto;

  let pqrServiceMock: any;
  let contractScoreServiceMock: any;
  let chainScoreServiceMock: any;

  const payerId1 = 'cus_1';
  const payerId2 = 'cus_2';
  const after10days = new Date(
    Date.now() + 1000 /*sec*/ * 60 /*min*/ * 60 /*hour*/ * 24 /*day*/ * 10,
  );
  const before10days = new Date(
    Date.now() - 1000 /*sec*/ * 60 /*min*/ * 60 /*hour*/ * 24 /*day*/ * 10,
  );

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    pqrService = module.get<PqrService>(PqrService);
    contractScoreService =
      module.get<ContractScoreService>(ContractScoreService);
    chainScoreService = module.get<ChainScoreService>(ChainScoreService);
    userService = module.get<UserService>(UserService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    await userRepository.assignPayerId({ id: user1.id, payerId: payerId1 });
    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: after10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });
    user1Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });

    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    await userRepository.assignPayerId({ id: user2.id, payerId: payerId2 });
    await userService.updateSubscription({
      payerId: payerId2,
      permissions: 0, // all denied
      permissionExpiration: after10days,
      apiPermissions: 0, // all denied
      apiPermissionExpiration: after10days,
    });
    user2Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_2,
      password: TEST_DEFAULT_PASSWORD,
    });

    //mock all controllers (only guards/strategies testing)
    //only first method, because of access decorators applied to full class
    pqrServiceMock = jest
      .spyOn(pqrService, 'getPqrs')
      .mockImplementation(() => null);
    contractScoreServiceMock = jest
      .spyOn(contractScoreService, 'getScore')
      .mockImplementation(() => null);
    chainScoreServiceMock = jest
      .spyOn(chainScoreService, 'getChainScores')
      .mockImplementation(() => null);
  });

  it('should have access to all restricted endpoints through Bearer authentication', async () => {
    const pqrsRes = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(pqrsRes.status).toBe(200);

    const contractScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(contractScoresRes.status).toBe(200);

    const chainScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/chain-scores')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(chainScoresRes.status).toBe(200);
  });

  it('should have access to all restricted endpoints through X-Api-Key', async () => {
    const { apiPermissionToken } = await userRepository.findOne({
      id: user1.id,
    });
    const pqrsRes = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(pqrsRes.status).toBe(200);

    const contractScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(contractScoresRes.status).toBe(200);

    const chainScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/chain-scores')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(chainScoresRes.status).toBe(200);
  });

  it('should deny access to all restricted endpoints through Bearer authentication, allow PQRs only', async () => {
    const pqrsRes = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user2Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(pqrsRes.status).toBe(200);

    const contractScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user2Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(contractScoresRes.status).toBe(403);
    expect(contractScoresRes.body.message).toBe(ERROR_HTTP_FORBIDDEN);

    const chainScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/chain-scores')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user2Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(chainScoresRes.status).toBe(403);
  });

  it('should deny access to all restricted endpoints through X-Api-Key', async () => {
    const { apiPermissionToken } = await userRepository.findOne({
      id: user2.id,
    });
    const pqrsRes = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(pqrsRes.status).toBe(403);
    expect(pqrsRes.body.message).toBe(ERROR_HTTP_FORBIDDEN);

    const contractScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/contract-scores')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(contractScoresRes.status).toBe(403);

    const chainScoresRes = await supertest
      .agent(app.getHttpServer())
      .get('/chain-scores')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(chainScoresRes.status).toBe(403);
  });

  it('should deny access because permissions date expired', async () => {
    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: before10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/chain-scores')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(403);
    expect(body.message).toBe(ERROR_HTTP_FORBIDDEN);

    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: after10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });
  });

  it('PQRs should allow access to all users by default by web', async () => {
    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 0, // all granted
      permissionExpiration: before10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });

    const { status } = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);

    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: after10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });
  });

  it('should deny access by api token because api permissions date expired', async () => {
    const { apiPermissionToken } = await userRepository.findOne({
      id: user1.id,
    });

    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: after10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: before10days,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set('X-Api-Key', apiPermissionToken)
      .send();

    expect(status).toBe(403);
    expect(body.message).toBe(ERROR_HTTP_FORBIDDEN);

    await userService.updateSubscription({
      payerId: payerId1,
      permissions: 15, // all granted
      permissionExpiration: after10days,
      apiPermissions: 7, // all granted
      apiPermissionExpiration: after10days,
    });
  });

  it('should deny access, no application key provided', async () => {
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(401);
    expect(body.message).toBe(ERROR_HTTP_UNAUTHORIZED);
  });

  it('should deny access, wrong application key provided', async () => {
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .get('/pqrs')
      .set({ 'X-Application-Key': 'qwerty' })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(401);
    expect(body.message).toBe(ERROR_HTTP_UNAUTHORIZED);
  });

  afterAll(async () => {
    pqrServiceMock.mockRestore();
    contractScoreServiceMock.mockRestore();
    chainScoreServiceMock.mockRestore();

    await app.close();
    await schema.deleteSchema();
  });
});
