import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_NOT_FOUND,
} from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_3,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { EmailService } from '@modules/email/email.service';
import {
  EmailParamsDto,
  ForgotPasswordEmailParamsDto,
} from '@modules/email/dto/email.dto';

describe('Restore password flow', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userRepository: UserRepository;
  let emailService: EmailService;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    emailService = module.get<EmailService>(EmailService);

    await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });

    await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
  });

  it('should trigger password restoring and complete it by email token successfully', async () => {
    const newPassword = 'newTestSecurePassword123#';
    let passwordRefreshToken = '';
    const emailMock = jest
      .spyOn(emailService, 'sendForgotPasswordEmail')
      .mockImplementation(
        ({ params, to }: EmailParamsDto<ForgotPasswordEmailParamsDto>) => {
          expect(params.name).toBe(TEST_DEFAULT_EMAIL_1);
          expect(params.token.length).toBe(64);
          expect(to).toBe(TEST_DEFAULT_EMAIL_1);
          passwordRefreshToken = params.token;

          return Promise.resolve();
        },
      );
    const restore = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore')
      .send({ email: TEST_DEFAULT_EMAIL_1 });

    expect(restore.status).toBe(200);
    expect(restore.body.success).toBe(true);
    expect(passwordRefreshToken.length).toBe(64);

    const restoreConfirm = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore-confirm')
      .send({
        newPassword,
        passwordRefreshToken,
      });

    expect(restoreConfirm.status).toBe(200);
    expect(restoreConfirm.body.success).toBe(true);

    const restoreConfirmDuplicate = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore-confirm')
      .send({
        newPassword,
        passwordRefreshToken,
      });

    expect(restoreConfirmDuplicate.status).toBe(400);
    expect(restoreConfirmDuplicate.body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    const signInFailure = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD,
      });

    expect(signInFailure.status).toBe(404);
    expect(signInFailure.body.message).toBe(ERROR_HTTP_NOT_FOUND);

    const signIn = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: newPassword,
      });

    expect(signIn.status).toBe(200);
    expect(signIn.body).toHaveProperty('accessToken');
    expect(signIn.body).toHaveProperty('refreshToken');

    emailMock.mockRestore();
  });

  it('should throw an error (user not exist)', async () => {
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore')
      .send({ email: TEST_DEFAULT_EMAIL_3 });

    expect(status).toBe(404);
    expect(body.message).toBe(ERROR_HTTP_NOT_FOUND);
  });

  it('should throw an error (wrong confirmation token)', async () => {
    const emailMock = jest
      .spyOn(emailService, 'sendForgotPasswordEmail')
      .mockImplementation(() => {
        return Promise.resolve();
      });

    await supertest
      .agent(app.getHttpServer())
      .post('/user/restore')
      .send({ email: TEST_DEFAULT_EMAIL_1 });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore-confirm')
      .send({
        newPassword: TEST_DEFAULT_PASSWORD,
        passwordRefreshToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    emailMock.mockRestore();
  });

  it('should throw an error (expired token)', async () => {
    let passwordRefreshToken = '';
    const emailMock = jest
      .spyOn(emailService, 'sendForgotPasswordEmail')
      .mockImplementation(
        ({ params }: EmailParamsDto<ForgotPasswordEmailParamsDto>) => {
          passwordRefreshToken = params.token;

          return Promise.resolve();
        },
      );
    const now = Date.now();
    const dateNowMock = jest
      .spyOn(Date, 'now')
      .mockImplementation(() => new Date(now - 20 * 60 * 1000).getTime());

    await supertest
      .agent(app.getHttpServer())
      .post('/user/restore')
      .send({ email: TEST_DEFAULT_EMAIL_1 });

    dateNowMock.mockRestore();

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/restore-confirm')
      .send({
        newPassword: TEST_DEFAULT_PASSWORD,
        passwordRefreshToken,
      });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    emailMock.mockRestore();
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
