import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import { USER_REPOSITORY_TOKEN } from '@modules/user/tokens';
import { ERROR_HTTP_BAD_REQUEST } from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';
import { UserService } from '@modules/user/user.service';
import { User } from '@root/internal-db/models/User';

describe('Change password flow', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let userRepository: UserRepository;
  let user: User;
  let userTokens: SignInResponseDto;

  const newPassword = 'newTestSecurePassword123#';

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userService = module.get<UserService>(UserService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);

    user = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    userTokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });
  });

  it('should change password', async () => {
    const { id, password, salt } = user;

    const changePasswordRes = await supertest
      .agent(app.getHttpServer())
      .post('/user/change-password')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(userTokens.accessToken, { type: 'bearer' })
      .send({ password: TEST_DEFAULT_PASSWORD, newPassword });

    expect(changePasswordRes.status).toBe(200);
    expect(changePasswordRes.body.success).toBe(true);

    const updatedUser = await userRepository.findOne({ id: user.id });

    expect(updatedUser.password).not.toBe(user.password);
    expect(updatedUser.salt).not.toBe(user.salt);

    await userRepository.updatePassword({ id, password, salt });
  });

  it('should throw an error, old password is incorrect', async () => {
    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/change-password')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(userTokens.accessToken, { type: 'bearer' })
      .send({ password: newPassword, newPassword });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
