import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import { UserRepository } from '@modules/user/user.repository';
import {
  REFRESH_TOKEN_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN,
} from '@modules/user/tokens';
import {
  ERROR_HTTP_NOT_FOUND,
  ERROR_HTTP_UNAUTHORIZED,
  ERROR_VALIDATION_EMAIL,
} from '@config/error-messages';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_3,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { RefreshTokenRepository } from '@modules/user/refresh-token.repository';
import { User } from '@root/internal-db/models/User';

describe('Sign-in flow', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userRepository: UserRepository;
  let refreshTokenRepository: RefreshTokenRepository;
  let user1: User;
  let user2: User;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    refreshTokenRepository = module.get(REFRESH_TOKEN_REPOSITORY_TOKEN);

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });

    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
  });

  it('should sign-in successfully', async () => {
    let user1session1 = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD,
      });

    await new Promise((r) => setTimeout(r, 1100));

    const user1session2 = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD,
      });

    await new Promise((r) => setTimeout(r, 1100));

    const user1session3 = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD,
      });
    const user2session1 = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_2,
        password: TEST_DEFAULT_PASSWORD,
      });

    expect(user1session1.status).toBe(200);
    expect(user1session1.body).toHaveProperty('accessToken');
    expect(user1session1.body).toHaveProperty('refreshToken');
    expect(user1session2.status).toBe(200);
    expect(user1session2.body).toHaveProperty('accessToken');
    expect(user1session2.body).toHaveProperty('refreshToken');
    expect(user1session3.status).toBe(200);
    expect(user1session3.body).toHaveProperty('accessToken');
    expect(user1session3.body).toHaveProperty('refreshToken');
    expect(user2session1.status).toBe(200);
    expect(user2session1.body).toHaveProperty('accessToken');
    expect(user2session1.body).toHaveProperty('refreshToken');

    let refreshToken_1_1 = await refreshTokenRepository.findOne({ id: 1 });
    const refreshToken_1_2 = await refreshTokenRepository.findOne({ id: 2 });
    const refreshToken_1_3 = await refreshTokenRepository.findOne({ id: 3 });
    const refreshToken_2_1 = await refreshTokenRepository.findOne({ id: 4 });

    expect(refreshToken_1_1.userId).toBe(user1.id);
    expect(refreshToken_1_2.userId).toBe(user1.id);
    expect(refreshToken_1_3.userId).toBe(user1.id);
    expect(refreshToken_2_1.userId).toBe(user2.id);

    user1session1 = await supertest
      .agent(app.getHttpServer())
      .post('/user/refresh-tokens')
      .auth(user1session1.body.refreshToken, { type: 'bearer' })
      .send();

    expect(user1session1.status).toBe(200);
    expect(user1session1.body).toHaveProperty('accessToken');
    expect(user1session1.body).toHaveProperty('refreshToken');

    refreshToken_1_1 = await refreshTokenRepository.findOne({ id: 5 });

    const refreshToken_1_1_removed = await refreshTokenRepository.findOne({
      id: 1,
    });

    expect(refreshToken_1_1_removed).toBeUndefined();

    const signOutUser1Session1 = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-out')
      .auth(user1session1.body.refreshToken, { type: 'bearer' })
      .send();

    expect(signOutUser1Session1.status).toBe(200);
    expect(signOutUser1Session1.body.success).toBe(true);
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_1_1.refreshToken,
      }),
    ).toBeUndefined();
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_1_2.refreshToken,
      }),
    ).toBeDefined();
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_2_1.refreshToken,
      }),
    ).toBeDefined();

    const signOutUser1Session1Err = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-out')
      .auth(user1session1.body.refreshToken, { type: 'bearer' })
      .send();

    expect(signOutUser1Session1Err.status).toBe(401);
    expect(signOutUser1Session1Err.body.message).toBe(ERROR_HTTP_UNAUTHORIZED);

    const signOutUser1All = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-out/all')
      .auth(user1session2.body.refreshToken, { type: 'bearer' })
      .send();

    expect(signOutUser1All.status).toBe(200);
    expect(signOutUser1All.body.success).toBe(true);
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_1_2.refreshToken,
      }),
    ).toBeUndefined();
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_1_3.refreshToken,
      }),
    ).toBeUndefined();
    expect(
      await refreshTokenRepository.findOne({
        refreshToken: refreshToken_2_1.refreshToken,
      }),
    ).toBeDefined();

    const signOutUser1AllErr = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-out/all')
      .auth(user1session2.body.refreshToken, { type: 'bearer' })
      .send();

    expect(signOutUser1AllErr.status).toBe(401);
    expect(signOutUser1AllErr.body.message).toBe(ERROR_HTTP_UNAUTHORIZED);
  });

  it('should throw an error (not existing user)', async () => {
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_3,
        password: TEST_DEFAULT_PASSWORD,
      });

    expect(status).toBe(404);
    expect(body.message).toBe(ERROR_HTTP_NOT_FOUND);
  });

  it('should throw an error (wrong password)', async () => {
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: TEST_DEFAULT_EMAIL_1,
        password: TEST_DEFAULT_PASSWORD + '1',
      });

    expect(status).toBe(404);
    expect(body.message).toBe(ERROR_HTTP_NOT_FOUND);
  });

  it('should throw an error (invalid credentials)', async () => {
    const { body, status } = await supertest
      .agent(app.getHttpServer())
      .post('/user/sign-in')
      .send({
        email: 1111,
        password: 'small',
      });

    expect(status).toBe(400);
    expect(body.message).toContain(ERROR_VALIDATION_EMAIL);
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
