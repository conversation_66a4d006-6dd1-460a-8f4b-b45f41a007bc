import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { RandomSchema } from '@common/utils/random-schema';
import * as supertest from 'supertest';
import Web3 from 'web3';
import { UserRepository } from '@modules/user/user.repository';
import {
  UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN,
} from '@modules/user/tokens';
import {
  createTestingModule,
  TEST_DEFAULT_EMAIL_1,
  TEST_DEFAULT_EMAIL_2,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
  TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
  TEST_DEFAULT_PASSWORD,
  TEST_DEFAULT_PASSWORD_HASH,
  TEST_DEFAULT_PRIVATE_ADDRESS_1,
  TEST_DEFAULT_PUBLIC_ADDRESS_1,
  TEST_DEFAULT_SALT,
} from '@root/setup/e2e-test.setup';
import { User } from '@root/internal-db/models/User';
import {
  MAX_NONCE,
  UnverifiedPublicAddressRepository,
} from '@modules/user/unverified-public-address.repository';
import { SignInResponseDto } from '@modules/user/dto/sign-in.dto';
import { UserService } from '@modules/user/user.service';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_CONFLICT,
  ERROR_HTTP_UNPROCESSABLE_ENTITY,
} from '@config/error-messages';

describe('Web3 sign-in flow', () => {
  let app: INestApplication;
  let module: TestingModule;
  let schema: RandomSchema;
  let userService: UserService;
  let userRepository: UserRepository;
  let unverifiedPublicAddressRepository: UnverifiedPublicAddressRepository;
  let user1: User;
  let user2: User;
  let user1Tokens: SignInResponseDto;

  beforeAll(async () => {
    ({ module, schema } = await createTestingModule());
    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    userService = module.get<UserService>(UserService);
    userRepository = module.get(USER_REPOSITORY_TOKEN);
    unverifiedPublicAddressRepository = module.get(
      UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
    );

    user1 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_1,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
    user1Tokens = await userService.signInByEmail({
      email: TEST_DEFAULT_EMAIL_1,
      password: TEST_DEFAULT_PASSWORD,
    });
    user2 = await userRepository.create({
      email: TEST_DEFAULT_EMAIL_2,
      emailConfirmationToken: TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2,
      password: TEST_DEFAULT_PASSWORD_HASH,
      salt: TEST_DEFAULT_SALT,
    });
  });

  it('should generate nonce successfully', async () => {
    const nonceUser1Res = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/nonce')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    expect(nonceUser1Res.status).toBe(200);
    expect(nonceUser1Res.body.publicAddress).toBe(
      TEST_DEFAULT_PUBLIC_ADDRESS_1,
    );
    expect(nonceUser1Res.body.nonce).toBeGreaterThan(0);
    expect(nonceUser1Res.body.nonce).toBeLessThanOrEqual(MAX_NONCE);

    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.findOne({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    expect(unverifiedPublicAddress.nonce).toBe(nonceUser1Res.body.nonce);

    await unverifiedPublicAddressRepository.clear({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });
  });

  it('should sign-up by signature and cleanup nonce', async () => {
    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/sign-in')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(200);
    expect(body).toHaveProperty('accessToken');

    const user = await userRepository.findOne({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    expect(user).toBeDefined();

    const nonce = await unverifiedPublicAddressRepository.findOne({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    expect(nonce).toBeUndefined();

    await userRepository.delete({ id: user.id });
  });

  it('should throw an error, unverified record not exists or already used', async () => {
    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      '********',
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/sign-in')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);
  });

  it('should throw an error, incorrect signature', async () => {
    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      (unverifiedPublicAddress.nonce + 1).toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/sign-in')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    await unverifiedPublicAddressRepository.clear({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });
  });

  it('should sign-in by signature and cleanup nonce', async () => {
    const user = await userRepository.createByWeb3({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    const nonceRes = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/nonce')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    expect(nonceRes.status).toBe(200);
    expect(nonceRes.body.nonce).toBeGreaterThan(0);

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      nonceRes.body.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/sign-in')
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(200);
    expect(body).toHaveProperty('accessToken');

    await userRepository.delete({ id: user.id });
  });

  it('should attach public address to existing account', async () => {
    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/attach-address')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(200);
    expect(body.success).toBe(true);

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.publicAddress).toBe(TEST_DEFAULT_PUBLIC_ADDRESS_1);

    await userRepository.updatePublicAddress({
      id: user1.id,
      publicAddress: null,
    });
  });

  it('should throw an error on address attach, wallet already used in the system', async () => {
    await userRepository.updatePublicAddress({
      id: user2.id,
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/attach-address')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
        signature: signature.signature,
      });

    expect(status).toBe(409);
    expect(body.message).toBe(ERROR_HTTP_CONFLICT);

    await userRepository.updatePublicAddress({
      id: user2.id,
      publicAddress: null,
    });
  });

  it('should throw an error on address attach, trying same wallet in other case sensitivity', async () => {
    await userRepository.updatePublicAddress({
      id: user2.id,
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1.toLowerCase(),
      });

    const web3 = new Web3();
    const signature = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .post('/user/web3/attach-address')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1.toLowerCase(),
        signature: signature.signature,
      });

    expect(status).toBe(400);
    expect(body.message).toBe(ERROR_HTTP_BAD_REQUEST);

    await userRepository.updatePublicAddress({
      id: user2.id,
      publicAddress: null,
    });
  });

  it('should remove wallet from account', async () => {
    await userRepository.updatePublicAddress({
      id: user1.id,
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .delete('/user/web3')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(user1Tokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(200);
    expect(body.success).toBe(true);

    const user = await userRepository.findOne({ id: user1.id });

    expect(user.publicAddress).toBeNull();
  });

  it('should throw an error, because account have no email', async () => {
    const unverifiedPublicAddress =
      await unverifiedPublicAddressRepository.create({
        publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      });

    const web3 = new Web3();
    const { signature } = web3.eth.accounts.sign(
      unverifiedPublicAddress.nonce.toString(),
      TEST_DEFAULT_PRIVATE_ADDRESS_1,
    );
    const userTokens = await userService.web3SignIn({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
      signature,
    });

    const { status, body } = await supertest
      .agent(app.getHttpServer())
      .delete('/user/web3')
      .set({ 'X-Application-Key': process.env.X_APPLICATION_KEY })
      .auth(userTokens.accessToken, { type: 'bearer' })
      .send();

    expect(status).toBe(422);
    expect(body.message).toBe(ERROR_HTTP_UNPROCESSABLE_ENTITY);

    await unverifiedPublicAddressRepository.clear({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    const user = await userRepository.findOne({
      publicAddress: TEST_DEFAULT_PUBLIC_ADDRESS_1,
    });

    await userRepository.delete({ id: user.id });
  });

  afterAll(async () => {
    await app.close();
    await schema.deleteSchema();
  });
});
