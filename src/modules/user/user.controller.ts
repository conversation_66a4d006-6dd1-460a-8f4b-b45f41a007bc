import {
  Body,
  Request,
  Controller,
  Get,
  Post,
  UseGuards,
  HttpCode,
  Param,
  Delete,
} from '@nestjs/common';
import { UserService } from '@modules/user/user.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiUnauthorizedResponse,
  ApiUnprocessableEntityResponse,
} from '@nestjs/swagger';
import {
  SignInRequestDto,
  SignInResponseDto,
} from '@modules/user/dto/sign-in.dto';
import { JwtAuthGuard } from '@modules/user/jwt.guard';
import {
  ConfirmEmailRequestDto,
  ConfirmEmailResponseDto,
  SignUpUserByEmailDto,
} from '@modules/user/dto/sign-up.dto';
import { JwtRefreshAuthGuard } from '@modules/user/jwt-refresh.guard';
import { SignOutResponseDto } from '@modules/user/dto/sign-out.dto';
import {
  AccessStrategyDto,
  RefreshStrategyDto,
} from '@modules/user/dto/strategy.dto';
import { UserResponseDto } from '@modules/user/dto/user.dto';
import {
  Web3AttachAddressResponseDto,
  Web3NonceRequestDto,
  Web3NonceResponseDto,
  Web3SignInRequestDto,
} from '@modules/user/dto/web3.dto';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_CONFLICT,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_NOT_FOUND,
  ERROR_HTTP_UNAUTHORIZED,
  ERROR_HTTP_UNPROCESSABLE_ENTITY,
  ERROR_VALIDATION_USER_EXISTS,
} from '@config/error-messages';
import {
  RestoreConfirmRequestDto,
  RestoreConfirmResponseDto,
  RestoreRequestDto,
  RestoreResponseDto,
} from '@modules/user/dto/restore.dto';
import { RefreshApiTokenResponse } from '@modules/user/dto/payer.dto';
import {
  ChangePasswordRequestDto,
  ChangePasswordResponseDto,
} from '@modules/user/dto/change-password.dto';

@ApiTags('User')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('sign-in')
  @ApiOperation({ summary: 'Sign-in user using email and password.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: 'Bad request' })
  @ApiNotFoundResponse({ description: 'Not found' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  signIn(@Body() body: SignInRequestDto): Promise<SignInResponseDto> {
    return this.userService.signInByEmail(body);
  }

  @Post('refresh-tokens')
  @UseGuards(JwtRefreshAuthGuard)
  @ApiBearerAuth('refresh-token')
  @ApiOperation({ summary: 'Refresh current user token pair.' })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  refreshTokens(
    @Request() req: RefreshStrategyDto,
  ): Promise<SignInResponseDto> {
    return this.userService.refreshTokens({
      id: req.user.id,
      refreshToken: req.user.refreshToken,
    });
  }

  @Post('sign-out')
  @UseGuards(JwtRefreshAuthGuard)
  @ApiBearerAuth('refresh-token')
  @ApiOperation({ summary: 'Sign-out current device.' })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  signOut(@Request() req: RefreshStrategyDto): Promise<SignOutResponseDto> {
    return this.userService.signOut({
      id: req.user.id,
      refreshToken: req.user.refreshToken,
    });
  }

  @Post('sign-out/all')
  @UseGuards(JwtRefreshAuthGuard)
  @ApiBearerAuth('refresh-token')
  @ApiOperation({ summary: 'Sign-out all devices.' })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  signOutAll(@Request() req: RefreshStrategyDto): Promise<SignOutResponseDto> {
    return this.userService.signOut({ id: req.user.id });
  }

  @Post('sign-up')
  @ApiOperation({ summary: 'Sign-up user using email and password.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnprocessableEntityResponse({ description: ERROR_VALIDATION_USER_EXISTS })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  signUp(@Body() body: SignUpUserByEmailDto): Promise<SignInResponseDto> {
    return this.userService.signUpByEmail(body);
  }

  @Get('confirm-email/:token')
  @ApiOperation({
    summary: 'Verify email by clicking on link with confirmation token.',
  })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  confirmEmail(
    @Param() params: ConfirmEmailRequestDto,
  ): Promise<ConfirmEmailResponseDto> {
    return this.userService.confirmEmail(params);
  }

  @Post('restore')
  @ApiOperation({
    summary:
      'Recover forgotten password via email (first step: sending confirmation email).',
  })
  @HttpCode(200)
  @ApiNotFoundResponse({ description: ERROR_HTTP_NOT_FOUND })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  restore(@Body() body: RestoreRequestDto): Promise<RestoreResponseDto> {
    return this.userService.restore(body);
  }

  @Post('restore-confirm')
  @ApiOperation({
    summary:
      'Recover forgotten password via email (second step: send new password with email token).',
  })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  restoreConfirm(
    @Body() body: RestoreConfirmRequestDto,
  ): Promise<RestoreConfirmResponseDto> {
    return this.userService.restoreConfirm(body);
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({ summary: 'Change password.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  changePassword(
    @Request() req: AccessStrategyDto,
    @Body() body: ChangePasswordRequestDto,
  ): Promise<ChangePasswordResponseDto> {
    return this.userService.changePassword({ id: req.user.id, ...body });
  }

  @Post('refresh-api-token')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({ summary: 'Refresh API token.' })
  @HttpCode(200)
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  refreshApiToken(
    @Request() req: AccessStrategyDto,
  ): Promise<RefreshApiTokenResponse> {
    return this.userService.refreshApiToken({ id: req.user.id });
  }

  @Post('/web3/nonce')
  @ApiOperation({ summary: 'Retrieve nonce by public address.' })
  @HttpCode(200)
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  web3Nonce(@Body() body: Web3NonceRequestDto): Promise<Web3NonceResponseDto> {
    return this.userService.web3GenerateNonce(body);
  }

  @Post('/web3/sign-in')
  @HttpCode(200)
  @ApiOperation({ summary: 'Sign-in by signature and public address.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiNotFoundResponse({ description: ERROR_HTTP_NOT_FOUND })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  web3SignIn(@Body() body: Web3SignInRequestDto): Promise<SignInResponseDto> {
    return this.userService.web3SignIn(body);
  }

  @Post('/web3/attach-address')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @HttpCode(200)
  @ApiOperation({ summary: 'Verify Web3 address and attach it to account.' })
  @ApiBadRequestResponse({ description: ERROR_HTTP_BAD_REQUEST })
  @ApiConflictResponse({ description: ERROR_HTTP_CONFLICT })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  web3AttachSignAddress(
    @Request() req: AccessStrategyDto,
    @Body() body: Web3SignInRequestDto,
  ): Promise<Web3AttachAddressResponseDto> {
    return this.userService.web3AttachSignAddress({
      userId: req.user.id,
      publicAddress: body.publicAddress,
      signature: body.signature,
    });
  }

  @Delete('/web3')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @HttpCode(200)
  @ApiOperation({ summary: 'Remove connected web3 wallet from account.' })
  @ApiUnprocessableEntityResponse({
    description: ERROR_HTTP_UNPROCESSABLE_ENTITY,
  })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  web3DeleteSignAddress(
    @Request() req: AccessStrategyDto,
  ): Promise<Web3AttachAddressResponseDto> {
    return this.userService.web3DeleteSignAddress({ id: req.user.id });
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity({ 'access-token': [], 'x-application-key': [] })
  @ApiOperation({ summary: 'Get current user.' })
  @ApiUnauthorizedResponse({ description: ERROR_HTTP_UNAUTHORIZED })
  @ApiNotFoundResponse({ description: ERROR_HTTP_NOT_FOUND })
  @ApiInternalServerErrorResponse({
    description: ERROR_HTTP_INTERNAL_SERVER_ERROR,
  })
  getUser(@Request() req: AccessStrategyDto): Promise<UserResponseDto> {
    return this.userService.getUserById({ id: req.user.id });
  }
}
