import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { UserController } from '@modules/user/user.controller';
import { UserService } from '@modules/user/user.service';
import { JwtStrategy } from '@modules/user/jwt.strategy';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@root/internal-db/models/User';
import { UserRepository } from '@modules/user/user.repository';
import {
  REFRESH_TOKEN_REPOSITORY_TOKEN,
  UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN,
} from '@modules/user/tokens';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { RefreshTokenRepository } from '@modules/user/refresh-token.repository';
import { RefreshToken } from '@root/internal-db/models/RefreshToken';
import { JwtRefreshStrategy } from '@modules/user/jwt-refresh.strategy';
import { EmailModule } from '@modules/email/email.module';
import { JwtApiKeyStrategy } from '@modules/user/jwt-api-key.strategy';
import { UnverifiedPublicAddressRepository } from '@modules/user/unverified-public-address.repository';
import { UnverifiedPublicAddress } from '@root/internal-db/models/UnverifiedPublicAddress';
import { ConfigModule } from '@nestjs/config';

@Module({
  controllers: [UserController],
  imports: [
    TypeOrmModule.forFeature(
      [User, RefreshToken, UnverifiedPublicAddress],
      INTERNAL_DB_CONNECTION,
    ),
    JwtModule.register({}),
    EmailModule,
    ConfigModule,
  ],
  providers: [
    UserService,
    JwtStrategy,
    JwtApiKeyStrategy,
    JwtRefreshStrategy,
    {
      provide: USER_REPOSITORY_TOKEN,
      useClass: UserRepository,
    },
    {
      provide: REFRESH_TOKEN_REPOSITORY_TOKEN,
      useClass: RefreshTokenRepository,
    },
    {
      provide: UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
      useClass: UnverifiedPublicAddressRepository,
    },
  ],
  exports: [
    JwtModule,
    USER_REPOSITORY_TOKEN,
    REFRESH_TOKEN_REPOSITORY_TOKEN,
    UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
    UserService,
  ],
})
export class UserModule {}
