import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserService } from './user.service';
import { REFRESH_STRATEGY } from '@modules/user/tokens';
import { Request } from 'express';
import { RefreshStrategyValidateDto } from '@modules/user/dto/strategy.dto';
import { ERROR_HTTP_UNAUTHORIZED } from '@config/error-messages';

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(
  Strategy,
  REFRESH_STRATEGY,
) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_REFRESH_TOKEN_SECRET,
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    payload: any,
  ): Promise<RefreshStrategyValidateDto> {
    const refreshToken = ExtractJwt.fromAuthHeaderAsBearerToken()(req as any);
    const tokenRecord = await this.userService.getRefreshTokenWithUser({
      refreshToken,
    });

    if (!tokenRecord?.User?.id || tokenRecord.User.id !== payload.id) {
      throw new UnauthorizedException(ERROR_HTTP_UNAUTHORIZED);
    }

    return { id: tokenRecord.User.id, refreshToken };
  }
}
