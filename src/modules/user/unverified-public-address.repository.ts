import { Injectable } from '@nestjs/common';
import { DeleteResult, FindConditions, Repository } from 'typeorm';
import * as crypto from 'crypto';
import { InjectRepository } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { UnverifiedPublicAddress } from '@root/internal-db/models/UnverifiedPublicAddress';
import { DeleteUnverifiedPublicAddress } from '@modules/user/dto/unferified-public-address.dto';
import { Web3NonceRequestDto } from '@modules/user/dto/web3.dto';

export const MAX_NONCE = 10000000;

@Injectable()
export class UnverifiedPublicAddressRepository {
  constructor(
    @InjectRepository(UnverifiedPublicAddress, INTERNAL_DB_CONNECTION)
    private readonly unverifiedPublicAddressRepository: Repository<UnverifiedPublicAddress>,
  ) {}

  findOne(
    condition: FindConditions<UnverifiedPublicAddress>,
  ): Promise<UnverifiedPublicAddress | undefined> {
    return this.unverifiedPublicAddressRepository.findOne(condition);
  }

  async create({
    publicAddress,
  }: Web3NonceRequestDto): Promise<UnverifiedPublicAddress> {
    let unverifiedPublicAddress =
      await this.unverifiedPublicAddressRepository.findOne({ publicAddress });

    if (!unverifiedPublicAddress) {
      unverifiedPublicAddress = this.unverifiedPublicAddressRepository.create({
        publicAddress,
      });
    }

    unverifiedPublicAddress.nonce = crypto.randomInt(1, MAX_NONCE);

    return this.unverifiedPublicAddressRepository.save(unverifiedPublicAddress);
  }

  clear({
    publicAddress,
  }: DeleteUnverifiedPublicAddress): Promise<DeleteResult> {
    return this.unverifiedPublicAddressRepository.delete({ publicAddress });
  }
}
