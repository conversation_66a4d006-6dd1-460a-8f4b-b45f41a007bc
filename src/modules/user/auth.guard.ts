import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import {
  API_PERMISSIONS_LIST,
  Permissions,
  PLAN_PERMISSIONS_LIST,
} from '@common/utils/permissions';
import {
  ROLE_METADATA_KEY,
  API_ROLE_METADATA_KEY,
} from '@common/decorators/role.decorator';
import { ERROR_HTTP_FORBIDDEN } from '@config/error-messages';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const role = this.reflector.get<
      (typeof PLAN_PERMISSIONS_LIST)[keyof typeof PLAN_PERMISSIONS_LIST]
    >(ROLE_METADATA_KEY, context.getClass());
    const apiRole = this.reflector.get<
      (typeof API_PERMISSIONS_LIST)[keyof typeof API_PERMISSIONS_LIST]
    >(API_ROLE_METADATA_KEY, context.getClass());

    if (!role && !apiRole) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (
      (role &&
        user.hasOwnProperty('permissions') &&
        (!Permissions.checkPlanPermission(user.permissions, role) ||
          new Date(user.permissionExpiration).getTime() < Date.now())) ||
      (apiRole &&
        user.hasOwnProperty('apiPermissions') &&
        (!Permissions.checkApiPermission(user.apiPermissions, apiRole) ||
          new Date(user.apiPermissionExpiration).getTime() < Date.now()))
    ) {
      throw new ForbiddenException(ERROR_HTTP_FORBIDDEN);
    }

    return true;
  }
}
