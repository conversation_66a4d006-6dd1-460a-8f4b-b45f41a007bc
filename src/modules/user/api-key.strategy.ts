import { Request } from 'express';
import { Strategy as PassportStrategy } from 'passport-strategy';
import { BadRequestException, HttpStatus } from '@nestjs/common';

export class Strategy extends PassportStrategy {
  apiKeyHeader: string;
  name: string;
  verify: (
    apiKey: string,
    verified: (
      err: Error | null,
      user?: Record<string, unknown>,
      info?: Record<string, unknown>,
    ) => void,
    req?: Request,
  ) => void;

  constructor(
    header: string,
    verify: (
      apiKey: string,
      verified: (
        err: Error | null,
        user?: Record<string, unknown>,
        info?: Record<string, unknown>,
      ) => void,
      req?: Request,
    ) => void,
  ) {
    super();
    this.apiKeyHeader = (header || 'X-Api-Key').toLowerCase();

    this.name = 'apikey';
    this.verify = verify;
  }

  authenticate(req: Request): void {
    const apiKey: string = req.headers[this.apiKeyHeader] as string;

    if (!apiKey) {
      return this.fail(
        new BadRequestException('Missing API Key'),
        HttpStatus.UNAUTHORIZED,
      );
    }

    const verified = (
      err: Error | null,
      user?: Record<string, unknown>,
      info?: Record<string, unknown>,
    ) => {
      if (err) {
        return this.error(err);
      }

      if (!user) {
        return this.fail(info, HttpStatus.UNAUTHORIZED);
      }

      this.success(user, info);
    };

    this.verify(apiKey, verified);
  }
}
