import { Strategy } from '@modules/user/api-key.strategy';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserService } from './user.service';
import { ERROR_HTTP_UNAUTHORIZED } from '@config/error-messages';
import { API_KEY_STRATEGY } from '@modules/user/tokens';
import { ApiKeyStrategyValidateDto } from '@modules/user/dto/strategy.dto';

@Injectable()
export class JwtApiKeyStrategy extends PassportStrategy(
  Strategy,
  API_KEY_STRATEGY,
) {
  constructor(private readonly userService: UserService) {
    super('X-Api-Key', (apiKey, done) => this.validate(apiKey, done));
  }

  async validate(
    apiKey: string,
    done: (error: Error, data) => ApiKeyStrategyValidateDto,
  ) {
    const user = await this.userService.getUserByCondition({
      apiPermissionToken: apiKey,
    });

    if (!user || !user.id) {
      return done(new UnauthorizedException(ERROR_HTTP_UNAUTHORIZED), null);
    }

    const { id, apiPermissions, apiPermissionExpiration } = user;

    return done(null, { id, apiPermissions, apiPermissionExpiration });
  }
}
