import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Matches, MinLength } from 'class-validator';
import {
  ERROR_VALIDATION_EMAIL,
  ERROR_VALIDATION_PASSWORD,
} from '@config/error-messages';

export interface RestoreDto {
  id: string;
  passwordRefreshToken: string;
  passwordRefreshExpiration: Date;
}

export interface UpdatePasswordDto {
  id: string;
  password: string;
  salt: string;
}

export class RestoreRequestDto {
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsString()
  @IsEmail({}, { message: ERROR_VALIDATION_EMAIL })
  email: string;
}

export class RestoreResponseDto {
  @ApiProperty()
  success: boolean;
}

export class RestoreConfirmRequestDto {
  @ApiProperty({
    description:
      'Requirements: minimum length - 8 symbols, must contain at least one digit, must contain at least one special character (from the list: !@#$%^&*)',
    example: 'password1#',
  })
  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[\d])(?=.*[!@#$%^&*])[\w!@#$%^&*]{8,}$/, {
    message: ERROR_VALIDATION_PASSWORD,
  })
  newPassword: string;

  @ApiProperty()
  @IsString()
  passwordRefreshToken: string;
}

export class RestoreConfirmResponseDto {
  @ApiProperty()
  success: boolean;
}
