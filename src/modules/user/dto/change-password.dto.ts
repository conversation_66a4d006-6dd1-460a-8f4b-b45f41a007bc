import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches, MinLength } from 'class-validator';
import { ERROR_VALIDATION_PASSWORD } from '@config/error-messages';

export interface ChangePassword {
  id: string;
  password: string;
  newPassword: string;
}

export class ChangePasswordRequestDto {
  @ApiProperty({
    example: 'password1#',
  })
  @IsString()
  password: string;

  @ApiProperty({
    description:
      'Requirements: minimum length - 8 symbols, must contain at least one digit, must contain at least one special character (from the list: !@#$%^&*)',
    example: 'password1#',
  })
  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[\d])(?=.*[!@#$%^&*])[\w!@#$%^&*]{8,}$/, {
    message: ERROR_VALIDATION_PASSWORD,
  })
  newPassword: string;
}

export class ChangePasswordResponseDto {
  @ApiProperty()
  success: boolean;
}
