import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';
import { ERROR_VALIDATION_EMAIL } from '@config/error-messages';

export class SignInRequestDto {
  @ApiProperty({
    format: 'email',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: ERROR_VALIDATION_EMAIL })
  @IsString()
  email: string;

  @ApiProperty({
    example: 'password1#',
  })
  @IsString()
  password: string;
}

export class SignInResponseDto {
  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;
}
