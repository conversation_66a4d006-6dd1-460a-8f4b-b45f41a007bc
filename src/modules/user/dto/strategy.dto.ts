export interface AccessStrategyValidateDto {
  id: string;
  permissions: number;
  permissionExpiration: Date;
}

export interface AccessStrategyDto {
  user: AccessStrategyValidateDto;
}

export interface RefreshStrategyValidateDto {
  id: string;
  refreshToken: string;
}

export interface RefreshStrategyDto {
  user: RefreshStrategyValidateDto;
}

export interface ApiKeyStrategyValidateDto {
  id: string;
  apiPermissions: number;
  apiPermissionExpiration: Date;
}
