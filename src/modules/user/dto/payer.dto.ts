export interface AssignPayer {
  id: string;
  payerId: string;
}

export interface AssignPayerResponse {
  success: boolean;
}

export interface UpdateSubscriptionBasic {
  permissions?: number;
  permissionExpiration: Date;
  apiPermissions?: number;
  apiPermissionExpiration?: Date;
}

export interface UpdateSubscription extends UpdateSubscriptionBasic {
  payerId: string;
}

export interface UpdateSubscriptionCrypto extends UpdateSubscriptionBasic {
  id: string;
}

export interface UpdateSubscriptionFields {
  permissions?: number;
  permissionExpiration?: Date;
  apiPermissions?: number;
  apiPermissionExpiration?: Date;
}

export interface UpdateByPayerId {
  payerId: string;
}

export interface UpdateByPublicAddress {
  id: string;
}

export interface UpdateSubscriptionParams {
  updateBy: UpdateByPayerId | UpdateByPublicAddress;
  fieldsToUpdate: UpdateSubscriptionFields;
}

export interface UpdateSubscriptionResponse {
  success: boolean;
}

export interface RefreshApiTokenParams {
  id: string;
  apiPermissionToken: string;
}

export interface RefreshApiTokenResponse {
  apiPermissionToken: string;
}
