import { ApiProperty } from '@nestjs/swagger';
import { PagingResponseDto } from '@common/dto/paging-response.dto';
import { OrderDto } from '@common/dto/order.dto';
import { UnfinishedPaymentDetails } from '@root/modules/payment/interfaces/unfinished-payment.interface';

export interface GetUserByCondition {
  id?: string;
  email?: string;
  emailConfirmationToken?: string;
  emailConfirmed?: boolean;
  publicAddress?: string;
  passwordRefreshToken?: string;
  passwordRefreshExpiration?: Date;
  payerId?: string;
  apiPermissionToken?: string;
}

export interface CreateUserByPassword {
  email: string;
  emailConfirmationToken: string;
  password: string;
  salt: string;
}

export interface CreateUserByWeb3 {
  publicAddress: string;
}

export interface CreateUserByAdmin {
  email?: string;
  password?: string;
  salt?: string;
  publicAddress?: string;
}

export interface UpdateUserByAdmin {
  id: string;
  email?: string;
  publicAddress?: string;
  permissions?: number;
  permissionExpiration?: Date;
  apiPermissions?: number;
  apiPermissionExpiration?: Date;
}

export interface UpdateUserWeb3Wallet {
  id: string;
  publicAddress: string;
}

export interface ClearNonce {
  id: string;
}

export class UserResponseDto {
  @ApiProperty({
    type: 'uuid',
    format: 'uuid',
  })
  public id: string;

  @ApiProperty({
    nullable: true,
    maxLength: 255,
    format: 'email',
  })
  public email: string;

  @ApiProperty()
  public emailConfirmed: boolean;

  @ApiProperty({
    nullable: true,
    maxLength: 255,
  })
  public publicAddress: string;

  @ApiProperty({
    nullable: false,
    default: 0,
  })
  public permissions: number;

  @ApiProperty({
    type: Date,
    nullable: true,
  })
  public permissionExpiration: Date;

  @ApiProperty({
    nullable: false,
    default: 0,
  })
  public apiPermissions: number;

  @ApiProperty({
    nullable: true,
    minLength: 64,
    maxLength: 64,
  })
  public apiPermissionToken: string;

  @ApiProperty({
    type: Date,
    nullable: true,
  })
  public apiPermissionExpiration: Date;

  @ApiProperty({
    nullable: false,
    default: false,
  })
  public unfinishedPayment: boolean;

  @ApiProperty({
    nullable: true,
    default: null,
  })
  public unfinishedPaymentDetails: UnfinishedPaymentDetails;

  @ApiProperty({
    type: Date,
  })
  public createdAt: Date;

  @ApiProperty({
    type: Date,
  })
  public updatedAt: Date;
}

export interface UsersPaginatedRequest {
  search?: string;
  orderBy?: string;
  order?: OrderDto;
  offset: number;
  limit: number;
}

export class UsersResponsePagingDto extends PagingResponseDto<UserResponseDto> {
  @ApiProperty({ type: [UserResponseDto] })
  data: UserResponseDto[];
}

export interface UpdateUserResponse {
  success: boolean;
}
