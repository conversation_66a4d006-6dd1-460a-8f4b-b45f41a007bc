import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Matches, MinLength } from 'class-validator';
import {
  ERROR_VALIDATION_EMAIL,
  ERROR_VALIDATION_PASSWORD,
} from '@config/error-messages';

export interface SignUpUserByEmail {
  email: string;
  password: string;
}

export interface SignUpUserByMetamask {
  publicAddress: string;
}

export interface ConfirmEmailRequest {
  token: string;
}

export interface SignUpUserByAdmin {
  email?: string;
  password?: string;
  publicAddress?: string;
  permissions?: number;
  permissionExpiration?: Date;
  apiPermissions?: number;
  apiPermissionExpiration?: Date;
}

export class SignUpUserByEmailDto implements SignUpUserByEmail {
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsString()
  @IsEmail({}, { message: ERROR_VALIDATION_EMAIL })
  email: string;

  @ApiProperty({
    description:
      'Requirements: minimum length - 8 symbols, must contain at least one digit, must contain at least one special character (from the list: !@#$%^&*)',
    example: 'password1#',
  })
  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[\d])(?=.*[!@#$%^&*])[\w!@#$%^&*]{8,}$/, {
    message: ERROR_VALIDATION_PASSWORD,
  })
  password: string;
}

export class SignUpUserByMetamaskDto implements SignUpUserByMetamask {
  @ApiProperty()
  @IsString()
  publicAddress: string;
}

export class ConfirmEmailRequestDto implements ConfirmEmailRequest {
  @ApiProperty()
  @IsString()
  token: string;
}

export class ConfirmEmailResponseDto {
  @ApiProperty()
  success: boolean;
}
