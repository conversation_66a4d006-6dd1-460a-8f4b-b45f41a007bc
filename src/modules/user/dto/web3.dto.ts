import { IsBoolean, IsN<PERSON>ber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Web3NonceRequestDto {
  @ApiProperty()
  @IsString()
  publicAddress: string;
}

export class Web3NonceResponseDto {
  @ApiProperty()
  @IsNumber()
  nonce: number;

  @ApiProperty()
  @IsString()
  publicAddress: string;
}

export class Web3SignInRequestDto {
  @ApiProperty()
  @IsString()
  publicAddress: string;

  @ApiProperty()
  @IsString()
  signature: string;
}

export class Web3AttachAddressResponseDto {
  @ApiProperty({
    type: Boolean,
  })
  @IsBoolean()
  success: boolean;
}
