import { Injectable } from '@nestjs/common';
import { Delete<PERSON><PERSON><PERSON>, <PERSON>Than, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import { RefreshToken } from '@root/internal-db/models/RefreshToken';
import {
  CreateRefreshToken,
  RefreshTokenWithUserDto,
} from '@modules/user/dto/refresh-token.dto';
import { SignOutDto } from '@modules/user/dto/sign-out.dto';

@Injectable()
export class RefreshTokenRepository {
  constructor(
    @InjectRepository(RefreshToken, INTERNAL_DB_CONNECTION)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
  ) {}

  findOne(
    condition: Record<string, unknown>,
  ): Promise<RefreshToken | undefined> {
    return this.refreshTokenRepository.findOne(condition);
  }

  async create(refreshTokenData: CreateRefreshToken): Promise<RefreshToken> {
    // Delete expired tokens of user
    const passedDate = new Date(
      Date.now() - +process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME * 1000,
    ).toISOString();

    await this.refreshTokenRepository.delete({
      userId: refreshTokenData.userId,
      updatedAt: LessThan(passedDate),
    });

    const refreshToken = this.refreshTokenRepository.create(refreshTokenData);

    return this.refreshTokenRepository.save(refreshToken);
  }

  findOneWithUser({
    refreshToken,
  }: RefreshTokenWithUserDto): Promise<RefreshToken> {
    return this.refreshTokenRepository.findOne(
      { refreshToken },
      { relations: ['User'] },
    );
  }

  delete({ id, refreshToken }: SignOutDto): Promise<DeleteResult> {
    const condition = { userId: id };

    if (refreshToken) {
      condition['refreshToken'] = refreshToken;
    }

    return this.refreshTokenRepository.delete(condition);
  }
}
