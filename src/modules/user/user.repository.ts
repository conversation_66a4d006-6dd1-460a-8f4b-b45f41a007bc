import { Injectable } from '@nestjs/common';
import {
  DeleteResult,
  FindConditions,
  ILike,
  Repository,
  UpdateResult,
} from 'typeorm';
import { User } from '@root/internal-db/models/User';
import { InjectRepository } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';
import {
  CreateUserByAdmin,
  CreateUserByPassword,
  CreateUserByWeb3,
  UpdateUserByAdmin,
  UpdateUserWeb3Wallet,
  UsersPaginatedRequest,
} from '@modules/user/dto/user.dto';
import { RestoreDto, UpdatePasswordDto } from '@modules/user/dto/restore.dto';
import {
  AssignPayer,
  RefreshApiTokenParams,
  UpdateSubscriptionParams,
} from '@modules/user/dto/payer.dto';
import { OrderDto } from '@common/dto/order.dto';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User, INTERNAL_DB_CONNECTION)
    private readonly userRepository: Repository<User>,
  ) {}

  findOne(condition: FindConditions<User>): Promise<User | undefined> {
    return this.userRepository.findOne(condition);
  }

  findAndCount({
    search,
    orderBy,
    order,
    limit,
    offset,
  }: UsersPaginatedRequest): Promise<[User[], number]> {
    let where;

    if (search) {
      const searchQuery = `%${search}%`;

      where = [
        { email: ILike(searchQuery) },
        { publicAddress: ILike(searchQuery) },
      ];
    }

    return this.userRepository.findAndCount({
      where,
      order: {
        ...(orderBy && { [orderBy]: order || OrderDto.DESC }),
        id: OrderDto.ASC,
      },
      skip: offset,
      take: limit,
    });
  }

  create(userData: CreateUserByPassword): Promise<User> {
    const user = this.userRepository.create(userData);

    return this.userRepository.save(user);
  }

  createByAdmin(userData: CreateUserByAdmin): Promise<User> {
    const user = this.userRepository.create({
      ...userData,
      emailConfirmed: true,
    });

    return this.userRepository.save(user);
  }

  updateByAdmin({
    id,
    email,
    publicAddress,
  }: UpdateUserByAdmin): Promise<UpdateResult> {
    return this.userRepository.update({ id }, { email, publicAddress });
  }

  async createByWeb3({ publicAddress }: CreateUserByWeb3): Promise<User> {
    const user = this.userRepository.create({ publicAddress });

    return this.userRepository.save(user);
  }

  async updatePublicAddress({
    id,
    publicAddress,
  }: UpdateUserWeb3Wallet): Promise<UpdateResult> {
    return this.userRepository.update({ id }, { publicAddress });
  }

  confirmEmail({ id }): Promise<UpdateResult> {
    return this.userRepository.update(
      { id },
      { emailConfirmed: true, emailConfirmationToken: null },
    );
  }

  restore({
    id,
    passwordRefreshToken,
    passwordRefreshExpiration,
  }: RestoreDto): Promise<UpdateResult> {
    return this.userRepository.update(
      { id },
      { passwordRefreshToken, passwordRefreshExpiration },
    );
  }

  updatePassword({
    id,
    password,
    salt,
  }: UpdatePasswordDto): Promise<UpdateResult> {
    return this.userRepository.update(
      { id },
      {
        password,
        salt,
        passwordRefreshToken: null,
        passwordRefreshExpiration: null,
      },
    );
  }

  assignPayerId({ id, payerId }: AssignPayer): Promise<UpdateResult> {
    return this.userRepository.update({ id }, { payerId });
  }

  updateSubscription({
    updateBy,
    fieldsToUpdate,
  }: UpdateSubscriptionParams): Promise<UpdateResult> {
    return this.userRepository.update(updateBy, fieldsToUpdate);
  }

  updateById(
    id: string,
    params: QueryDeepPartialEntity<User>,
  ): Promise<UpdateResult> {
    return this.userRepository.update({ id }, params);
  }

  refreshApiToken({
    id,
    apiPermissionToken,
  }: RefreshApiTokenParams): Promise<UpdateResult> {
    return this.userRepository.update({ id }, { apiPermissionToken });
  }

  delete({ id }: { id: string }): Promise<DeleteResult> {
    return this.userRepository.delete({ id });
  }
}
