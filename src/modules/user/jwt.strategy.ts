import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserService } from './user.service';
import { AccessStrategyValidateDto } from '@modules/user/dto/strategy.dto';
import { ERROR_HTTP_UNAUTHORIZED } from '@config/error-messages';
import { ACCESS_STRATEGY } from '@modules/user/tokens';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, ACCESS_STRATEGY) {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_ACCESS_TOKEN_SECRET'),
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    payload: any,
  ): Promise<AccessStrategyValidateDto> {
    const applicationKey = req.headers['x-application-key'];
    const user = await this.userService.getUserByCondition({ id: payload.id });

    if (
      !user ||
      !user.id ||
      !applicationKey ||
      applicationKey !== this.configService.get('X_APPLICATION_KEY')
    ) {
      throw new UnauthorizedException(ERROR_HTTP_UNAUTHORIZED);
    }

    const { id, permissions, permissionExpiration } = user;

    return { id, permissions, permissionExpiration };
  }
}
