import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ACCESS_STRATEGY } from '@modules/user/tokens';

@Injectable()
export class JwtAuthGuard extends AuthGuard(ACCESS_STRATEGY) {
  optional?: boolean;

  constructor(props: { optional?: boolean } = {}) {
    super();
    this.optional = !!props.optional;
  }

  handleRequest(err, user) {
    if (err || !user) {
      if (this.optional) {
        return true;
      }

      throw err || new UnauthorizedException();
    }

    return user;
  }
}
