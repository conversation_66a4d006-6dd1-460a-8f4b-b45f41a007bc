import {
  Injectable,
  Inject,
  NotFoundException,
  UnprocessableEntityException,
  BadRequestException,
  Logger,
  InternalServerErrorException,
  ConflictException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from '@root/internal-db/models/User';
import {
  SignInRequestDto,
  SignInResponseDto,
} from '@modules/user/dto/sign-in.dto';
import {
  REFRESH_TOKEN_REPOSITORY_TOKEN,
  UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN,
} from '@modules/user/tokens';
import { UserRepository } from '@modules/user/user.repository';
import * as crypto from 'crypto';
import Web3 from 'web3';
import {
  ConfirmEmailRequestDto,
  ConfirmEmailResponseDto,
  SignUpUserByAdmin,
  SignUpUserByEmailDto,
} from '@modules/user/dto/sign-up.dto';
import { RefreshTokenRepository } from '@modules/user/refresh-token.repository';
import { RefreshToken } from '@root/internal-db/models/RefreshToken';
import {
  RefreshTokenDto,
  RefreshTokenWithUserDto,
} from '@modules/user/dto/refresh-token.dto';
import { SignOutDto, SignOutResponseDto } from '@modules/user/dto/sign-out.dto';
import {
  GetUserByCondition,
  UpdateUserByAdmin,
  UpdateUserResponse,
  UserResponseDto,
  UsersPaginatedRequest,
  UsersResponsePagingDto,
} from '@modules/user/dto/user.dto';
import {
  Web3AttachAddressResponseDto,
  Web3NonceRequestDto,
  Web3NonceResponseDto,
  Web3SignInRequestDto,
} from '@modules/user/dto/web3.dto';
import { EmailService } from '@modules/email/email.service';
import {
  ERROR_HTTP_BAD_REQUEST,
  ERROR_HTTP_CONFLICT,
  ERROR_HTTP_INTERNAL_SERVER_ERROR,
  ERROR_HTTP_NOT_FOUND,
  ERROR_HTTP_UNPROCESSABLE_ENTITY,
  ERROR_VALIDATION_EMAIL_OR_ADDRESS,
  ERROR_VALIDATION_USER_EXISTS,
} from '@config/error-messages';
import {
  RestoreConfirmRequestDto,
  RestoreConfirmResponseDto,
  RestoreRequestDto,
  RestoreResponseDto,
} from '@modules/user/dto/restore.dto';
import {
  AssignPayer,
  AssignPayerResponse,
  RefreshApiTokenResponse,
  UpdateSubscription,
  UpdateSubscriptionCrypto,
  UpdateSubscriptionFields,
  UpdateSubscriptionResponse,
} from '@modules/user/dto/payer.dto';
import { UpdateResult } from 'typeorm';
import { UnverifiedPublicAddressRepository } from '@modules/user/unverified-public-address.repository';
import {
  ChangePassword,
  ChangePasswordResponseDto,
} from '@modules/user/dto/change-password.dto';
import { StartPaymentDto } from '../payment/dto/start-payment.dto';

const HASH_ALGO = 'sha512';
const HASH_ENCODING = 'hex';
const ENCODING = 'utf8';
export const PASSWORD_RESTORE_EXPIRATION_TIME = 15 * 60 * 1000; // 15 minutes

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private jwtService: JwtService,
    private readonly emailService: EmailService,
    @Inject(USER_REPOSITORY_TOKEN)
    private readonly userRepository: UserRepository,
    @Inject(REFRESH_TOKEN_REPOSITORY_TOKEN)
    private readonly refreshTokenRepository: RefreshTokenRepository,
    @Inject(UNVERIFIED_PUBLIC_ADDRESS_REPOSITORY_TOKEN)
    private readonly unverifiedPublicAddressRepository: UnverifiedPublicAddressRepository,
  ) {}

  private static getSaltedHash(password: string, salt?: string): string {
    const hash = crypto.createHash(HASH_ALGO);

    hash.update(password, ENCODING);

    if (salt) {
      hash.update(salt, ENCODING);
    }

    return hash.digest(HASH_ENCODING);
  }

  private static hashPassword({ password }: { password: string }): {
    hash: string;
    salt: string;
  } {
    const salt = crypto.randomBytes(10).toString(HASH_ENCODING);

    return { hash: UserService.getSaltedHash(password, salt), salt };
  }

  private static castUserToUserResponse({
    id,
    email,
    emailConfirmed,
    publicAddress,
    permissions,
    permissionExpiration,
    apiPermissions,
    apiPermissionToken,
    apiPermissionExpiration,
    unfinishedPayment,
    unfinishedPaymentDetails,
    createdAt,
    updatedAt,
  }: User): UserResponseDto {
    return {
      id,
      email,
      emailConfirmed,
      publicAddress,
      permissions,
      permissionExpiration,
      apiPermissions,
      apiPermissionToken,
      apiPermissionExpiration,
      unfinishedPayment,
      unfinishedPaymentDetails,
      createdAt,
      updatedAt,
    };
  }

  private async getTokens({ id }: { id: string }): Promise<SignInResponseDto> {
    const payload = { id };
    const accessToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_ACCESS_TOKEN_SECRET,
      expiresIn: +process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME,
    });
    const refreshToken = this.jwtService.sign(payload, {
      secret: process.env.JWT_REFRESH_TOKEN_SECRET,
      expiresIn: +process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME,
    });
    const refreshTokenHash = UserService.getSaltedHash(refreshToken);

    await this.refreshTokenRepository.create({
      userId: id,
      refreshToken: refreshTokenHash,
    });

    return { accessToken, refreshToken };
  }

  private async isSignatureValid({
    publicAddress,
    nonce,
    signature,
  }): Promise<boolean> {
    const web3 = new Web3();
    const address = await web3.eth.accounts.recover(
      nonce.toString(),
      signature,
    );

    return address === publicAddress;
  }

  getUserByCondition(
    conditions: GetUserByCondition,
  ): Promise<User | undefined> {
    return this.userRepository.findOne(conditions);
  }

  getRefreshTokenWithUser({
    refreshToken,
  }: RefreshTokenWithUserDto): Promise<RefreshToken> {
    const refreshTokenHash = UserService.getSaltedHash(refreshToken);

    return this.refreshTokenRepository.findOneWithUser({
      refreshToken: refreshTokenHash,
    });
  }

  async signInByEmail({
    email,
    password,
  }: SignInRequestDto): Promise<SignInResponseDto> {
    const user = await this.getUserByCondition({ email });

    if (
      !user ||
      UserService.getSaltedHash(password, user.salt) !== user.password
    ) {
      throw new NotFoundException();
    }

    return this.getTokens({ id: user.id });
  }

  async refreshTokens({
    id,
    refreshToken,
  }: RefreshTokenDto): Promise<SignInResponseDto> {
    const refreshTokenHash = UserService.getSaltedHash(refreshToken);

    await this.refreshTokenRepository.delete({
      id,
      refreshToken: refreshTokenHash,
    });

    return this.getTokens({ id });
  }

  async signOut({ id, refreshToken }: SignOutDto): Promise<SignOutResponseDto> {
    const payload: SignOutDto = { id };

    if (refreshToken) {
      payload.refreshToken = UserService.getSaltedHash(refreshToken);
    }

    const res = await this.refreshTokenRepository.delete(payload);

    return { success: !!res.affected };
  }

  async signUpByEmail({
    email,
    password,
  }: SignUpUserByEmailDto): Promise<SignInResponseDto> {
    const existingUser = await this.getUserByCondition({ email });

    if (existingUser) {
      throw new UnprocessableEntityException(ERROR_VALIDATION_USER_EXISTS);
    }

    const { hash, salt } = UserService.hashPassword({ password });
    const emailConfirmationToken = crypto.randomBytes(32).toString('hex');
    const user = await this.userRepository.create({
      email,
      emailConfirmationToken,
      password: hash,
      salt,
    });

    await this.emailService.sendConfirmationEmail({
      params: {
        host: process.env.FRONT_END_HOST,
        name: email,
        token: emailConfirmationToken,
      },
      to: email,
    });

    return this.getTokens({ id: user.id });
  }

  async signUpByAdmin({
    email,
    password,
    publicAddress,
    permissions,
    permissionExpiration,
    apiPermissions,
    apiPermissionExpiration,
  }: SignUpUserByAdmin): Promise<string> {
    let user;

    if (email && password) {
      const existingUser = await this.getUserByCondition({ email });

      if (existingUser) {
        throw new UnprocessableEntityException(ERROR_VALIDATION_USER_EXISTS);
      }

      const { hash, salt } = UserService.hashPassword({ password });

      user = await this.userRepository.createByAdmin({
        email,
        publicAddress,
        password: hash,
        salt,
      });
    } else if (publicAddress) {
      const existingUser = await this.getUserByCondition({ publicAddress });

      if (existingUser) {
        throw new UnprocessableEntityException(ERROR_VALIDATION_USER_EXISTS);
      }

      user = await this.userRepository.createByAdmin({
        email,
        publicAddress,
      });
    } else {
      throw new UnprocessableEntityException(ERROR_VALIDATION_EMAIL_OR_ADDRESS);
    }

    if (permissions || apiPermissions) {
      await this.updateSubscription({
        id: user.id,
        permissions,
        permissionExpiration,
        apiPermissions,
        apiPermissionExpiration,
      });
    }

    return user.id;
  }

  async confirmEmail({
    token,
  }: ConfirmEmailRequestDto): Promise<ConfirmEmailResponseDto> {
    const user = await this.getUserByCondition({
      emailConfirmationToken: token,
      emailConfirmed: false,
    });

    if (!user) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    await this.userRepository.confirmEmail({ id: user.id });

    return { success: true };
  }

  async web3GenerateNonce({
    publicAddress,
  }: Web3NonceRequestDto): Promise<Web3NonceResponseDto> {
    const unverifiedPublicAddress =
      await this.unverifiedPublicAddressRepository.create({ publicAddress });

    return {
      publicAddress: unverifiedPublicAddress.publicAddress,
      nonce: unverifiedPublicAddress.nonce,
    };
  }

  async restore({ email }: RestoreRequestDto): Promise<RestoreResponseDto> {
    const user = await this.getUserByCondition({ email });

    if (!user) {
      throw new NotFoundException(ERROR_HTTP_NOT_FOUND);
    }

    const passwordRefreshToken = crypto.randomBytes(32).toString('hex');
    const passwordRefreshExpiration = new Date(
      Date.now() + PASSWORD_RESTORE_EXPIRATION_TIME,
    );

    await this.userRepository.restore({
      id: user.id,
      passwordRefreshToken,
      passwordRefreshExpiration,
    });
    await this.emailService.sendForgotPasswordEmail({
      params: {
        host: process.env.FRONT_END_HOST,
        name: email,
        token: passwordRefreshToken,
      },
      to: email,
    });

    return { success: true };
  }

  async restoreConfirm({
    newPassword,
    passwordRefreshToken,
  }: RestoreConfirmRequestDto): Promise<RestoreConfirmResponseDto> {
    const user = await this.getUserByCondition({ passwordRefreshToken });

    if (!user) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    const timestamp = new Date(user.passwordRefreshExpiration).getTime();

    if (timestamp < Date.now()) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    const { hash, salt } = UserService.hashPassword({ password: newPassword });

    await this.userRepository.updatePassword({
      id: user.id,
      password: hash,
      salt,
    });

    return { success: true };
  }

  async changePassword({
    id,
    password,
    newPassword,
  }: ChangePassword): Promise<ChangePasswordResponseDto> {
    const user = await this.getUserByCondition({ id });

    if (
      !user ||
      UserService.getSaltedHash(password, user.salt) !== user.password
    ) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    const { hash, salt } = UserService.hashPassword({ password: newPassword });

    await this.userRepository.updatePassword({
      id: user.id,
      password: hash,
      salt,
    });

    return { success: true };
  }

  async web3SignIn({
    publicAddress,
    signature,
  }: Web3SignInRequestDto): Promise<any> {
    const existingUser = await this.getUserByCondition({ publicAddress });
    const unverifiedPublicAddress =
      await this.unverifiedPublicAddressRepository.findOne({ publicAddress });

    if (
      !unverifiedPublicAddress ||
      !(await this.isSignatureValid({
        publicAddress,
        nonce: unverifiedPublicAddress.nonce,
        signature,
      }))
    ) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    let tokens: SignInResponseDto;

    if (existingUser) {
      tokens = await this.getTokens({ id: existingUser.id });
    } else {
      const user = await this.userRepository.createByWeb3({ publicAddress });

      tokens = await this.getTokens({ id: user.id });
    }

    await this.unverifiedPublicAddressRepository.clear({ publicAddress });

    return tokens;
  }

  async web3AttachSignAddress({
    userId,
    publicAddress,
    signature,
  }): Promise<Web3AttachAddressResponseDto> {
    const user = await this.getUserByCondition({ id: userId });
    const existingUser = await this.getUserByCondition({ publicAddress });
    const unverifiedPublicAddress =
      await this.unverifiedPublicAddressRepository.findOne({ publicAddress });

    if (existingUser) {
      throw new ConflictException(ERROR_HTTP_CONFLICT);
    }

    if (
      !user ||
      !unverifiedPublicAddress ||
      !(await this.isSignatureValid({
        publicAddress,
        nonce: unverifiedPublicAddress.nonce,
        signature,
      }))
    ) {
      throw new BadRequestException(ERROR_HTTP_BAD_REQUEST);
    }

    const { affected } = await this.userRepository.updatePublicAddress({
      id: userId,
      publicAddress,
    });

    return { success: !!affected };
  }

  async web3DeleteSignAddress({ id }): Promise<Web3AttachAddressResponseDto> {
    const user = await this.getUserByCondition({ id });

    if (!user.email) {
      throw new UnprocessableEntityException(ERROR_HTTP_UNPROCESSABLE_ENTITY);
    }

    const { affected } = await this.userRepository.updatePublicAddress({
      id,
      publicAddress: null,
    });

    return { success: !!affected };
  }

  async getUserById({ id: userId }: { id: string }): Promise<UserResponseDto> {
    const user = await this.getUserByCondition({ id: userId });

    if (!user) {
      throw new NotFoundException(ERROR_HTTP_NOT_FOUND);
    }

    return UserService.castUserToUserResponse(user);
  }

  async getUsers(
    params: UsersPaginatedRequest,
  ): Promise<UsersResponsePagingDto> {
    const [users, total] = await this.userRepository.findAndCount(params);

    return {
      offset: Number(params.offset),
      limit: Number(params.limit),
      total,
      data: users.map(UserService.castUserToUserResponse),
    };
  }

  async assignPayerId({
    id,
    payerId,
  }: AssignPayer): Promise<AssignPayerResponse> {
    const user = await this.getUserByCondition({ id });

    if (user.payerId) {
      if (user.payerId === payerId) {
        return { success: true };
      } else {
        this.logger.warn(
          `Another payerId was assigned previously: ${user.payerId} -> ${payerId}`,
        );
      }
    }

    await this.userRepository.assignPayerId({ id, payerId });

    return { success: true };
  }

  async updateUserByAdmin({
    id,
    email,
    publicAddress,
    permissions,
    permissionExpiration,
    apiPermissions,
    apiPermissionExpiration,
  }: UpdateUserByAdmin): Promise<UpdateUserResponse> {
    const user = await this.getUserByCondition({ id });

    if (!user) {
    }

    await this.userRepository.updateByAdmin({ id, email, publicAddress });

    if (permissions || apiPermissions) {
      await this.updateSubscription({
        id,
        permissions,
        permissionExpiration,
        apiPermissions,
        apiPermissionExpiration,
      });
    }

    return { success: true };
  }

  async updateSubscription(
    params: UpdateSubscription | UpdateSubscriptionCrypto,
  ): Promise<UpdateSubscriptionResponse> {
    const fieldsToUpdate: UpdateSubscriptionFields = {};

    if (params.permissionExpiration || params.permissionExpiration === null) {
      fieldsToUpdate.permissionExpiration = params.permissionExpiration;
    }

    if (
      params.apiPermissionExpiration ||
      params.permissionExpiration === null
    ) {
      fieldsToUpdate.apiPermissionExpiration = params.apiPermissionExpiration;
    }

    if (params.permissions || params.permissions === 0) {
      fieldsToUpdate.permissions = params.permissions;
    }

    if (params.apiPermissions || params.apiPermissions === 0) {
      fieldsToUpdate.apiPermissions = params.apiPermissions;
    }

    const userIdentifier =
      'payerId' in params ? { payerId: params.payerId } : { id: params.id };

    const res: UpdateResult = await this.userRepository.updateSubscription({
      updateBy: userIdentifier,
      fieldsToUpdate,
    });

    const { id, apiPermissionToken } = await this.getUserByCondition(
      userIdentifier,
    );

    if (!apiPermissionToken) {
      await this.refreshApiToken({ id });
    }

    if (!res.affected) {
      this.logger.warn(`Not found payer: ${userIdentifier}`);
    }

    await this.userRepository.updateById(id, {
      unfinishedPayment: false,
      unfinishedPaymentDetails: null,
    });

    return { success: !!res.affected };
  }

  async refreshApiToken({ id }): Promise<RefreshApiTokenResponse> {
    const apiPermissionToken = crypto.randomBytes(32).toString('hex');
    const { affected } = await this.userRepository.refreshApiToken({
      id,
      apiPermissionToken,
    });

    if (!affected) {
      this.logger.error('Failed update of api permissions token');
      throw new InternalServerErrorException(ERROR_HTTP_INTERNAL_SERVER_ERROR);
    }

    return { apiPermissionToken };
  }

  async startPayment(id: string, data: StartPaymentDto) {
    const { affected } = await this.userRepository.updateById(id, {
      unfinishedPayment: true,
      unfinishedPaymentDetails: data,
    });

    if (!affected) {
      this.logger.error('Failed update of payment details');
      throw new InternalServerErrorException(ERROR_HTTP_INTERNAL_SERVER_ERROR);
    }

    this.logger.log(
      `Payment details successfully updated ${JSON.stringify(data)}`,
    );

    return { success: !!affected };
  }
}
