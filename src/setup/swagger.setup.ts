import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ContractScoreModule } from '@modules/contract-score/contract-score.module';
import { PqrModule } from '@modules/pqr/pqr.module';
import { ChainScoreModule } from '@modules/chain-score/chain-score.module';

const swaggerSetup = (app: any) => {
  const privateConfig = new DocumentBuilder()
    .setTitle('DeFi Safety API')
    .setDescription('')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Access token',
      },
      'access-token',
    )
    .addApiKey(
      {
        type: 'apiKey',
        description: 'Frontend application key',
        in: 'header',
        name: 'X-Application-Key',
      },
      'x-application-key',
    )
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Refresh token',
      },
      'refresh-token',
    )
    .addBearerAuth(
      {
        type: 'apiKey',
        description: 'API key',
        in: 'header',
        name: 'X-Api-Key',
      },
      'api-key',
    )
    .build();

  const config = new DocumentBuilder()
    .setTitle('DeFi Safety API')
    .setDescription('To use this API you should buy API key.')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'apiKey',
        description: 'API key',
        in: 'header',
        name: 'X-Api-Key',
      },
      'api-key',
    )
    .build();

  const privateAPI = SwaggerModule.createDocument(app, privateConfig);
  const contractScoresAPI = SwaggerModule.createDocument(app, config, {
    include: [ContractScoreModule],
  });
  const pqrsAPI = SwaggerModule.createDocument(app, config, {
    include: [PqrModule],
  });
  const chainScoresAPI = SwaggerModule.createDocument(app, config, {
    include: [ChainScoreModule],
  });

  SwaggerModule.setup('api-internal', app, privateAPI);
  SwaggerModule.setup('contract-scores-api', app, contractScoresAPI);
  SwaggerModule.setup('pqrs-api', app, pqrsAPI);
  SwaggerModule.setup('chain-scores-api', app, chainScoresAPI);
};

export default swaggerSetup;
