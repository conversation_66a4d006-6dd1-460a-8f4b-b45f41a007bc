import { Test } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { configuration, ConfigValidationSchema } from '@root/config';
import {
  getRepositoryToken,
  TypeOrmModule,
  TypeOrmModuleOptions,
} from '@nestjs/typeorm';
import { DATA_DB_CONNECTION, INTERNAL_DB_CONNECTION } from '@config/tokens';
import { RandomSchema } from '@common/utils/random-schema';
import { UserModule } from '@modules/user/user.module';
import { AppController } from '@root/app.controller';
import { AppService } from '@root/app.service';
import { AppConfig } from '@common/providers/app-config.provider';
import { FavouriteContractScore } from '@root/internal-db/models/FavouriteContractScore';
import { ContractScoreController } from '@modules/contract-score/contract-score.controller';
import { ContractScoreService } from '@modules/contract-score/contract-score.service';
import { FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN } from '@modules/contract-score-favourites/tokens';
import { FavouriteContractScoreRepository } from '@modules/contract-score-favourites/favourite-contract-score.repository';
import { Module } from '@nestjs/common';
import { ScoreHistoryEntity } from '@modules/contract-score/entities/score-history.entity';
import { Repository } from 'typeorm';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';
import { EmailModule } from '@modules/email/email.module';
import { PaymentGatewayModule } from '@modules/payment-gateway/payment-gateway.module';
import { PaymentModule } from '@modules/payment/payment.module';
import { ContractScoreFavouritesController } from '@modules/contract-score-favourites/contract-score-favourites.controller';
import { ContractScoreFavouritesService } from '@modules/contract-score-favourites/contract-score-favourites.service';
import { PqrModule } from '@modules/pqr/pqr.module';
import { ChainScoreModule } from '@modules/chain-score/chain-score.module';
import { PaymentCryptoModule } from '@modules/payment-crypto/payment-crypto.module';

export const TEST_DEFAULT_EMAIL_1 = '<EMAIL>';
export const TEST_DEFAULT_EMAIL_2 = '<EMAIL>';
export const TEST_DEFAULT_EMAIL_3 = '<EMAIL>';
export const TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_1 =
  '457e8318aee791bd794b4a42eae612b4a784c608280e28c9f4e227d4d7c9f5a7';
export const TEST_DEFAULT_EMAIL_CONFIRMATION_TOKEN_2 =
  'b67ad89fc00b0d758ed3ca6cf9caac67b5489aa464a59c0ccbf14d653405c837';
export const TEST_DEFAULT_PASSWORD = 'password_test_1#';
export const TEST_DEFAULT_SALT = '294cc39eab68bbdb22ab';
export const TEST_DEFAULT_PASSWORD_HASH =
  'b4828cf33a61a19149f896b5ab5614fc357a74d908b38464afd3e99dbaab9666367558561195fea2edf19cc027a98e386354224627448442783433a564486606';
export const TEST_DEFAULT_PUBLIC_ADDRESS_1 =
  '******************************************';
export const TEST_DEFAULT_PUBLIC_ADDRESS_2 =
  '******************************************';
export const TEST_DEFAULT_PRIVATE_ADDRESS_1 =
  '0c41c90d49a1c22b598d12e762ae4754614930462e86f2176770156c22b01b08';

@Module({
  imports: [
    TypeOrmModule.forFeature([FavouriteContractScore], INTERNAL_DB_CONNECTION),
  ],
  controllers: [ContractScoreController],
  providers: [
    ContractScoreService,
    {
      provide: getRepositoryToken(ScoreHistoryEntity, DATA_DB_CONNECTION),
      useClass: Repository,
    },
    {
      provide: getRepositoryToken(SearchableEntity, DATA_DB_CONNECTION),
      useClass: Repository,
    },
  ],
  exports: [
    ContractScoreService,
    getRepositoryToken(ScoreHistoryEntity, DATA_DB_CONNECTION),
    getRepositoryToken(SearchableEntity, DATA_DB_CONNECTION),
  ],
})
class ContractScoreModuleMock {}

@Module({
  imports: [
    TypeOrmModule.forFeature([FavouriteContractScore], INTERNAL_DB_CONNECTION),
    ContractScoreModuleMock,
  ],
  controllers: [ContractScoreFavouritesController],
  providers: [
    ContractScoreFavouritesService,
    {
      provide: FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN,
      useClass: FavouriteContractScoreRepository,
    },
    {
      provide: getRepositoryToken(SearchableEntity, DATA_DB_CONNECTION),
      useClass: Repository,
    },
  ],
  exports: [
    FAVOURITES_CONTRACT_SCORES_REPOSITORY_TOKEN,
    getRepositoryToken(SearchableEntity, DATA_DB_CONNECTION),
  ],
})
class ContractScoreFavouritesModuleMock {}

export const createTestingModule = async () => {
  let schema: RandomSchema;
  const module = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        load: configuration,
        validationSchema: ConfigValidationSchema,
        envFilePath: ['.env.local', '.env'],
      }),
      TypeOrmModule.forRootAsync({
        name: INTERNAL_DB_CONNECTION,
        useFactory: async () => {
          const testDb = {
            name: INTERNAL_DB_CONNECTION,
            type: process.env.DATABASE_CLIENT,
            host: process.env.DATABASE_HOST,
            port: +process.env.DATABASE_PORT,
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_NAME,
            entities: [__dirname + '/../internal-db/models/*{.ts,.js}'],
            migrationsTableName: 'migrations',
            migrations: [__dirname + '/../internal-db/migrations/*{.ts,.js}'],
            migrationsRun: true,
            retryAttempts: 3,
            retryDelay: 1000,
          };

          schema = new RandomSchema({
            user: testDb.username,
            host: testDb.host,
            database: testDb.database,
            password: testDb.password,
            port: testDb.port,
          });

          const schemaName = await schema.createSchema();
          const connectionOptions = { ...testDb, schema: schemaName };

          return connectionOptions as TypeOrmModuleOptions;
        },
      }),
      UserModule,
      ContractScoreModuleMock,
      ContractScoreFavouritesModuleMock,
      PqrModule,
      ChainScoreModule,
      EmailModule.forRoot({}),
      PaymentGatewayModule.forRoot({ apiKey: '' }),
      PaymentModule,
      PaymentCryptoModule.forRoot({
        CRYPTO_DEFISAFETY_CONTRACT_ADDRESS:
          '******************************************',
        CRYPTO_ETH_USDT_ADDRESS: '******************************************',
        CRYPTO_ETH_USDC_ADDRESS: '******************************************',
        CRYPTO_ETH_DAI_ADDRESS: '******************************************',
        CRYPTO_ARB_USDT_ADDRESS: '******************************************',
        CRYPTO_ARB_USDC_ADDRESS: '******************************************',
        CRYPTO_ARB_DAI_ADDRESS: '******************************************',
        CRYPTO_ETH_PROVIDER: '',
        CRYPTO_ARB_PROVIDER: '',
      }),
      // ScheduleModule.forRoot(),
    ],
    controllers: [AppController],
    providers: [AppService, AppConfig],
  }).compile();

  return { module, schema };
};
