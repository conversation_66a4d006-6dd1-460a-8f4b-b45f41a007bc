import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import swaggerSetup from './setup/swagger.setup';
import { NestExpressApplication } from '@nestjs/platform-express';
import { RequestInterceptor } from './common/interceptors/request.interceptor';

const logger = new Logger('App');

if (process.env.NODE_ENV === 'development') {
  Error.stackTraceLimit = 1000;

  process.on('unhandledRejection', (reason: Error) => {
    console.log(reason);
    logger.error(reason, reason.stack);
  });

  process.on('uncaughtExceptionMonitor', (err, origin) => {
    console.log(err, origin);
  });
}

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: false,
  });

  const configService = app.get(ConfigService);

  const { port } = configService.get('app');

  app.useGlobalInterceptors(new RequestInterceptor());
  app.useGlobalPipes(new ValidationPipe());
  app.enableCors();

  swaggerSetup(app);

  await app.listen(port, () => logger.log('API Service is listening...'));
}

bootstrap();
