import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class User1646156653503 implements MigrationInterface {
  name = 'User1646156653503';
  table_name = 'user';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = new Table({
      name: this.table_name,
      columns: [
        {
          name: 'id',
          isPrimary: true,
          type: 'serial',
          isNullable: false,
        },
        {
          name: 'email',
          type: 'varchar',
          length: '255',
          isNullable: true,
          isUnique: true,
        },
        {
          name: 'emailConfirmed',
          type: 'boolean',
          isNullable: false,
          default: false,
        },
        {
          name: 'emailConfirmationToken',
          type: 'varchar',
          length: '64',
          isNullable: true,
        },
        {
          name: 'password',
          type: 'varchar',
          length: '128',
          isNullable: true,
        },
        {
          name: 'salt',
          type: 'varchar',
          length: '20',
          isNullable: true,
        },
        {
          name: 'passwordRefreshToken',
          type: 'varchar',
          length: '64',
          isNullable: true,
        },
        {
          name: 'passwordRefreshExpiration',
          type: 'timestamp',
          precision: 6,
          isNullable: true,
        },
        {
          name: 'publicAddress',
          type: 'varchar',
          length: '255',
          isNullable: true,
          isUnique: true,
        },
        {
          name: 'nonce',
          type: 'int',
          isNullable: true,
        },
        {
          name: 'createdAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
        },
        {
          name: 'updatedAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
          onUpdate: 'CURRENT_TIMESTAMP',
        },
      ],
    });

    return queryRunner.createTable(table);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    return queryRunner.dropTable(this.table_name);
  }
}
