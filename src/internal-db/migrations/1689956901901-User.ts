import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class User1689956901901 implements MigrationInterface {
  name = 'User1689956901901';
  table_name = 'user';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const unfinishedPaymentColumn = new TableColumn({
      name: 'unfinishedPayment',
      type: 'boolean',
      isNullable: false,
      default: false,
    });

    const unfinishedPaymentDetailsColumn = new TableColumn({
      name: 'unfinishedPaymentDetails',
      type: 'jsonb',
      isNullable: true,
      default: null,
    });

    await queryRunner.addColumn(this.table_name, unfinishedPaymentColumn);
    await queryRunner.addColumn(
      this.table_name,
      unfinishedPaymentDetailsColumn,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.table_name, 'unfinishedPayment');
    await queryRunner.dropColumn(this.table_name, 'unfinishedPaymentDetails');
  }
}
