import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class PendingTransaction1651670767481 implements MigrationInterface {
  name = 'PendingTransaction1651670767481';
  table_name = 'pending_transaction';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = new Table({
      name: this.table_name,
      columns: [
        {
          name: 'id',
          isPrimary: true,
          type: 'uuid',
          isNullable: false,
          default: 'uuid_generate_v4()',
        },
        {
          name: 'userId',
          type: 'uuid',
          isNullable: false,
        },
        {
          name: 'transactionId',
          type: 'varchar',
          length: '255',
          isNullable: false,
          isUnique: true,
        },
        {
          name: 'priceId',
          type: 'varchar',
          length: '36',
          isNullable: false,
          isUnique: false,
        },
        {
          name: 'blockchain',
          type: 'varchar',
          length: '10',
          isNullable: false,
          isUnique: false,
        },
        {
          name: 'contract',
          type: 'varchar',
          length: '10',
          isNullable: false,
          isUnique: false,
        },
        {
          name: 'createdAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
        },
        {
          name: 'updatedAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
          onUpdate: 'CURRENT_TIMESTAMP',
        },
      ],
    });

    await queryRunner.createTable(table);

    const userForeignKey = new TableForeignKey({
      name: 'foreign_key_user',
      columnNames: ['userId'],
      referencedColumnNames: ['id'],
      referencedTableName: 'user',
      onDelete: 'CASCADE',
    });

    return queryRunner.createForeignKey(this.table_name, userForeignKey);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    return queryRunner.dropTable(this.table_name);
  }
}
