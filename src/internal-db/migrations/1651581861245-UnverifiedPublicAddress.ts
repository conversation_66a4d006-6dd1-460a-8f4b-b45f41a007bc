import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class UnverifiedPublicAddress1651581861245
  implements MigrationInterface
{
  name = 'UnverifiedPublicAddress1651581861245';
  table_name = 'unverified_public_address';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = new Table({
      name: this.table_name,
      columns: [
        {
          name: 'id',
          isPrimary: true,
          type: 'serial',
          isNullable: false,
        },
        {
          name: 'publicAddress',
          type: 'varchar',
          length: '255',
          isNullable: true,
          isUnique: true,
        },
        {
          name: 'nonce',
          type: 'int',
          isNullable: true,
        },
        {
          name: 'createdAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
        },
        {
          name: 'updatedAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
          onUpdate: 'CURRENT_TIMESTAMP',
        },
      ],
    });

    await queryRunner.createTable(table);

    return queryRunner.dropColumn('user', 'nonce');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const nonceColumn = new TableColumn({
      name: 'nonce',
      type: 'int',
      isNullable: true,
    });

    await queryRunner.dropTable(this.table_name);

    return queryRunner.addColumn('user', nonceColumn);
  }
}
