import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class PendingTransaction1651868354472 implements MigrationInterface {
  name = 'PendingTransaction1651868354472';
  table_name = 'pending_transaction';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const isProcessed = new TableColumn({
      name: 'isProcessed',
      type: 'boolean',
      default: false,
    });

    return queryRunner.addColumn(this.table_name, isProcessed);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    return queryRunner.dropColumn(this.table_name, 'isProcessed');
  }
}
