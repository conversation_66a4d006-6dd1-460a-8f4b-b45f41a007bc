import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class RefreshToken1646395617498 implements MigrationInterface {
  name = 'RefreshToken1646395617498';
  table_name = 'refresh_token';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = new Table({
      name: this.table_name,
      columns: [
        {
          name: 'id',
          isPrimary: true,
          type: 'serial',
          isNullable: false,
        },
        {
          name: 'userId',
          type: 'int',
          isNullable: false,
        },
        {
          name: 'refreshToken',
          type: 'varchar',
          length: '128',
          isNullable: false,
        },
        {
          name: 'createdAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
        },
        {
          name: 'updatedAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
          onUpdate: 'CURRENT_TIMESTAMP',
        },
      ],
    });

    await queryRunner.createTable(table);

    queryRunner.clearSqlMemory();

    const userForeignKey = new TableForeignKey({
      columnNames: ['userId'],
      referencedColumnNames: ['id'],
      referencedTableName: 'user',
      onDelete: 'CASCADE',
    });

    return queryRunner.createForeignKey(this.table_name, userForeignKey);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    return queryRunner.dropTable(this.table_name);
  }
}
