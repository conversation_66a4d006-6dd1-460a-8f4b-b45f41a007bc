import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class User1648649835556 implements MigrationInterface {
  name = 'User1648649835556';
  table_name = 'user';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const permissionsColumn = new TableColumn({
      name: 'permissions',
      type: 'integer',
      default: 0,
      isNullable: false,
    });
    const permissionExpirationColumn = new TableColumn({
      name: 'permissionExpiration',
      type: 'timestamp',
      isNullable: true,
      precision: 6,
    });
    const apiPermissionsColumn = new TableColumn({
      name: 'apiPermissions',
      type: 'integer',
      default: 0,
      isNullable: false,
    });
    const apiPermissionToken = new TableColumn({
      name: 'apiPermissionToken',
      type: 'varchar',
      length: '64',
      isNullable: true,
    });
    const apiPermissionExpirationColumn = new TableColumn({
      name: 'apiPermissionExpiration',
      type: 'timestamp',
      isNullable: true,
      precision: 6,
    });
    const payerColumn = new TableColumn({
      name: 'payerId',
      type: 'varchar',
      length: '64',
      isNullable: true,
    });

    const id = new TableColumn({
      name: 'id',
      isPrimary: true,
      type: 'uuid',
      isNullable: false,
      default: 'uuid_generate_v4()',
    });
    const userId = new TableColumn({
      name: 'userId',
      type: 'uuid',
      isNullable: false,
    });
    const userForeignKey = new TableForeignKey({
      name: 'foreign_key_user',
      columnNames: ['userId'],
      referencedColumnNames: ['id'],
      referencedTableName: 'user',
      onDelete: 'CASCADE',
    });

    await queryRunner.addColumn(this.table_name, permissionsColumn);
    await queryRunner.addColumn(this.table_name, permissionExpirationColumn);
    await queryRunner.addColumn(this.table_name, apiPermissionsColumn);
    await queryRunner.addColumn(this.table_name, apiPermissionToken);
    await queryRunner.addColumn(this.table_name, apiPermissionExpirationColumn);
    await queryRunner.addColumn(this.table_name, payerColumn);
    //Change User ID to UUID (v4) format
    await queryRunner.clearTable('favourite_contract_score');
    await queryRunner.dropColumn('favourite_contract_score', 'userId');
    await queryRunner.addColumn('favourite_contract_score', userId);
    await queryRunner.clearTable('refresh_token');
    await queryRunner.dropColumn('refresh_token', 'userId');
    await queryRunner.addColumn('refresh_token', userId);
    await queryRunner.changeColumn(this.table_name, 'id', id);
    await queryRunner.createForeignKey(
      'favourite_contract_score',
      userForeignKey,
    );
    await queryRunner.createForeignKey('refresh_token', userForeignKey);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.table_name, 'permissions');
    await queryRunner.dropColumn(this.table_name, 'permissionExpiration');
    await queryRunner.dropColumn(this.table_name, 'apiPermissions');
    await queryRunner.dropColumn(this.table_name, 'apiPermissionToken');
    await queryRunner.dropColumn(this.table_name, 'apiPermissionExpiration');

    return queryRunner.dropColumn(this.table_name, 'payerId');
  }
}
