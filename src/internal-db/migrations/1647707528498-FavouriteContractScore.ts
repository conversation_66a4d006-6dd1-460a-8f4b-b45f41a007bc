import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
  TableUnique,
} from 'typeorm';

export class FavouriteContractScore1647707528498 implements MigrationInterface {
  name = 'FavouriteContractScore1647707528498';
  table_name = 'favourite_contract_score';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = new Table({
      name: this.table_name,
      columns: [
        {
          name: 'id',
          isPrimary: true,
          type: 'serial',
          isNullable: false,
        },
        {
          name: 'userId',
          type: 'int',
          isNullable: false,
        },
        {
          name: 'contractScoreId',
          type: 'int',
          isNullable: false,
        },
        {
          name: 'createdAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
        },
        {
          name: 'updatedAt',
          type: 'timestamp',
          precision: 6,
          default: 'CURRENT_TIMESTAMP',
          onUpdate: 'CURRENT_TIMESTAMP',
        },
      ],
    });

    await queryRunner.createTable(table);

    queryRunner.clearSqlMemory();

    const userForeignKey = new TableForeignKey({
      columnNames: ['userId'],
      referencedColumnNames: ['id'],
      referencedTableName: 'user',
      onDelete: 'CASCADE',
    });

    await queryRunner.createForeignKey(this.table_name, userForeignKey);

    const uniqueSet = new TableUnique({
      name: 'favourite_contract_score_unique',
      columnNames: ['userId', 'contractScoreId'],
    });

    return queryRunner.createUniqueConstraint(this.table_name, uniqueSet);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    return queryRunner.dropTable(this.table_name);
  }
}
