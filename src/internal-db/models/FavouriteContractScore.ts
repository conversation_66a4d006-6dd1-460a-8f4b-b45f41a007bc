import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  BaseEntity,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { User } from '@root/internal-db/models/User';

@Entity()
@Unique('favourite_contract_score_unique', ['userId', 'contractScoreId'])
export class FavouriteContractScore extends BaseEntity {
  @PrimaryGeneratedColumn()
  public id: number;

  @Column({
    type: 'uuid',
    nullable: false,
  })
  public userId: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  public contractScoreId: number;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  public updatedAt: Date;

  @ManyToOne(() => User)
  @JoinColumn()
  public User: User;
}
