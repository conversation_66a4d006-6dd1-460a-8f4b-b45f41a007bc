import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  BaseEntity,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@root/internal-db/models/User';

@Entity()
export class RefreshToken extends BaseEntity {
  @PrimaryGeneratedColumn()
  @ApiProperty()
  public id: number;

  @Column({
    type: 'uuid',
    nullable: false,
  })
  @ApiProperty()
  public userId: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: false,
  })
  @ApiProperty()
  public refreshToken: string;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  @ApiProperty({ type: Date })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  @ApiProperty({ type: Date })
  public updatedAt: Date;

  @ManyToOne(() => User)
  @JoinColumn()
  public User: User;
}
