import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  BaseEntity,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class PendingTransaction extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({
    type: 'uuid',
    nullable: false,
  })
  public userId: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
    unique: true,
  })
  public transactionId: string;

  @Column({
    type: 'varchar',
    length: 36,
    nullable: false,
    unique: true,
  })
  public priceId: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: false,
    unique: false,
  })
  public blockchain: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: false,
    unique: false,
  })
  public contract: string;

  @Column({
    type: 'boolean',
    default: false,
  })
  public isProcessed: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  public updatedAt: Date;
}
