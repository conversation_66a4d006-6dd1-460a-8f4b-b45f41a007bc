import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  BaseEntity,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

import { UnfinishedPaymentDetails } from '@root/modules/payment/interfaces/unfinished-payment.interface';

@Entity()
export class User extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    unique: true,
  })
  public email: string;

  @Column({
    type: 'boolean',
    nullable: false,
    default: false,
  })
  public emailConfirmed: boolean;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
  })
  public emailConfirmationToken: string;

  @Column({
    type: 'varchar',
    length: 128,
    nullable: true,
  })
  public password: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  public salt: string;

  @Column({
    type: 'varchar',
    length: 64,
    nullable: true,
  })
  public passwordRefreshToken: string;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  public passwordRefreshExpiration: Date;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    nullable: true,
  })
  public publicAddress: string;

  @Column({
    type: 'integer',
    default: 0,
    nullable: false,
  })
  public permissions: number;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  public permissionExpiration: Date;

  @Column({
    type: 'integer',
    default: 0,
    nullable: false,
  })
  public apiPermissions: number;

  @Column({
    type: 'varchar',
    length: 64,
    unique: true,
    nullable: true,
  })
  public apiPermissionToken: string;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  public apiPermissionExpiration: Date;

  @Column({
    type: 'varchar',
    length: 64,
    unique: true,
    nullable: true,
  })
  public payerId: string;

  @Column({
    type: 'boolean',
    nullable: false,
    default: false,
  })
  public unfinishedPayment: boolean;

  @Column({
    type: 'jsonb',
    nullable: true,
    default: null,
  })
  public unfinishedPaymentDetails: UnfinishedPaymentDetails;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  public updatedAt: Date;
}
