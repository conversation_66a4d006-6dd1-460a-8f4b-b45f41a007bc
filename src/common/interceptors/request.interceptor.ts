import {
  <PERSON><PERSON><PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { sanitizeRequestParams } from '../utils/sanitizer';

@Injectable()
export class RequestInterceptor implements NestInterceptor {
  private logger = new Logger(RequestInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler) {
    const request = context.switchToHttp().getRequest();

    const params = {
      ...request.params,
      ...request.query,
      ...request.body,
    };

    const paramsStr = Object.keys(params).length
      ? JSON.stringify(sanitizeRequestParams(params))
      : '';

    this.logger.log(`${request.method} ${request.url} ${paramsStr}`);

    return next.handle().pipe();
  }
}
