import { SetMetadata } from '@nestjs/common';
import {
  API_PERMISSIONS_LIST,
  PLAN_PERMISSIONS_LIST,
} from '@common/utils/permissions';

export const ROLE_METADATA_KEY = 'ROLE_METADATA_KEY';
export const API_ROLE_METADATA_KEY = 'API_ROLE_METADATA_KEY';

export const Role = (
  role: (typeof PLAN_PERMISSIONS_LIST)[keyof typeof PLAN_PERMISSIONS_LIST],
) => SetMetadata(ROLE_METADATA_KEY, role);

export const ApiRole = (
  role: (typeof API_PERMISSIONS_LIST)[keyof typeof API_PERMISSIONS_LIST],
) => SetMetadata(API_ROLE_METADATA_KEY, role);
