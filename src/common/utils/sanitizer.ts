export const sanitizeRequestParams = (
  obj: Record<string, unknown> | unknown,
) => {
  if (typeof obj !== 'object') {
    return obj;
  }

  const newObj = { ...obj };

  Object.keys(newObj).forEach((key) => {
    if (typeof newObj[key] === 'object') {
      return sanitizeRequestParams(newObj[key]);
    }

    if (['password', 'confirmPassword'].includes(key)) {
      newObj[key] = '****';
    }
  });

  return newObj;
};
