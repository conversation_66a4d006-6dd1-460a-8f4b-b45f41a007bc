export const PLAN_PERMISSIONS_LIST = {
  PQRS: 0b1,
  CONTRACT_SCORES: 0b10,
  CHAIN_SCORES: 0b100,
  QUARTER_PRIVATE_REPORTS: 0b1000,
};

export const API_PERMISSIONS_LIST = {
  PQRS: 0b1,
  CONTRACT_SCORES: 0b10,
  CHAIN_SCORES: 0b100,
};

/**
 * User permissions stored in database as integer value (bitmask).
 * Check exact permission available by bitwise AND (&) operator.
 */
export class Permissions {
  static checkPlanPermission(
    decimalPermissions: number,
    permission: (typeof PLAN_PERMISSIONS_LIST)[keyof typeof PLAN_PERMISSIONS_LIST],
  ) {
    return (decimalPermissions & permission) === permission;
  }

  static checkApiPermission(
    decimalPermissions: number,
    permission: (typeof API_PERMISSIONS_LIST)[keyof typeof API_PERMISSIONS_LIST],
  ) {
    return (decimalPermissions & permission) === permission;
  }
}
