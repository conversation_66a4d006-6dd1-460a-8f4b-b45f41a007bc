import { Pool } from 'pg';

export interface RandomSchemaInit {
  user: string;
  host: string;
  database: string;
  password: string;
  port: number;
}

export class RandomSchema {
  private pool;
  private schemaPrefix = 'test_schema_';
  private schemaName;
  constructor(props: RandomSchemaInit) {
    this.pool = new Pool(props);
  }

  public async createSchema(): Promise<string> {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('should be called in test environment only');
    }

    const random = Math.floor(Math.random() * 100000);

    this.schemaName = this.schemaPrefix + random.toString();
    await this.pool.query(`CREATE SCHEMA "${this.schemaName}"`);

    return this.schemaName;
  }

  public async deleteSchema(): Promise<boolean> {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('should be called in test environment only');
    }

    if (!this.schemaName) {
      throw new Error('"createSchema" should be called first');
    }

    await this.pool.query(`DROP SCHEMA "${this.schemaName}" CASCADE`);
    await this.pool.end();

    return true;
  }
}
