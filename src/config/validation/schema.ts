import * as Joi from 'joi';
import Web3 from 'web3';

const web3AddressValidation = (val, helper) => {
  if (!Web3.utils.isAddress(val)) {
    return helper.message('Not valid Web3 address');
  }

  return val;
};

export const ConfigValidationSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test', 'local')
    .default('development'),
  PORT: Joi.number().default(3000),
  FRONT_END_HOST: Joi.string().required(),
  STRAPI_HOST: Joi.string().required(),
  ADMIN_API_KEY: Joi.string().required(),
  X_APPLICATION_KEY: Joi.string().required(),

  DATA_DB_HOST: Joi.string().required(),
  DATA_DB_PORT: Joi.number().default(1433),
  DATA_DB_USER: Joi.string().required(),
  DATA_DB_PASSWORD: Joi.string().required(),
  DATA_DB_NAME: Joi.string().required(),

  DATABASE_CLIENT: Joi.string().default('postgres'),
  DATABASE_HOST: Joi.string().required(),
  DATABASE_PORT: Joi.number().default(5432),
  DATABASE_USERNAME: Joi.string().required(),
  DATABASE_PASSWORD: Joi.string().required(),
  DATABASE_NAME: Joi.string().required(),
  DATABASE_SCHEMA: Joi.string().required(),

  JWT_ACCESS_TOKEN_SECRET: Joi.string().required(),
  JWT_ACCESS_TOKEN_EXPIRATION_TIME: Joi.number().required(),
  JWT_REFRESH_TOKEN_SECRET: Joi.string().required(),
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: Joi.number().required(),

  MAIL_HOST: Joi.string().required(),
  MAIL_PORT: Joi.number().required(),
  MAIL_USERNAME: Joi.string().required(),
  MAIL_PASSWORD: Joi.string().required(),

  STRIPE_API_KEY: Joi.string().required(),
  STRIPE_WEBHOOK_SECRET: Joi.string().required(),

  SENTRY_DSN: Joi.string().required(),

  CRYPTO_DEFISAFETY_CONTRACT_ADDRESS: Joi.string()
    .custom(web3AddressValidation)
    .required(),
  CRYPTO_ETH_USDT_ADDRESS: Joi.string()
    .custom(web3AddressValidation)
    .required(),
  CRYPTO_ETH_USDC_ADDRESS: Joi.string()
    .custom(web3AddressValidation)
    .required(),
  CRYPTO_ETH_DAI_ADDRESS: Joi.string().custom(web3AddressValidation).required(),
  CRYPTO_ARB_USDT_ADDRESS: Joi.string()
    .custom(web3AddressValidation)
    .required(),
  CRYPTO_ARB_USDC_ADDRESS: Joi.string()
    .custom(web3AddressValidation)
    .required(),
  CRYPTO_ARB_DAI_ADDRESS: Joi.string().custom(web3AddressValidation).required(),

  CRYPTO_ETH_PROVIDER: Joi.string().required(),
  CRYPTO_ARB_PROVIDER: Joi.string().required(),
});
