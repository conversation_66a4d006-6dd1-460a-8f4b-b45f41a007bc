import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ScoreHistoryEntity } from '@modules/contract-score/entities/score-history.entity';
import { DATA_DB_CONNECTION } from '@config/tokens';
import { SearchableEntity } from '@modules/contract-score/entities/searchable.entity';

export const dataDb = registerAs(
  DATA_DB_CONNECTION,
  (): TypeOrmModuleOptions => ({
    name: DATA_DB_CONNECTION,
    type: 'mssql',
    host: process.env.DATA_DB_HOST,
    port: +process.env.DATA_DB_PORT,
    username: process.env.DATA_DB_USER,
    password: process.env.DATA_DB_PASSWORD,
    database: process.env.DATA_DB_NAME,
    entities: [ScoreHistoryEntity, SearchableEntity],
    synchronize: false,
    options: { trustServerCertificate: true } as any,
    migrationsTableName: 'migrations',
    migrations: ['migrations/*.js'],
    migrationsRun: false,
    cli: {
      migrationsDir: 'migrations',
    },
    maxQueryExecutionTime: 20000,
    extra: {
      requestTimeout: 800000,
    },
    retryAttempts: 20,
    retryDelay: 4000,
  }),
);
