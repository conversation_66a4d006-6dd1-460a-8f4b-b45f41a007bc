import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { INTERNAL_DB_CONNECTION } from '@config/tokens';

export const internalDb = registerAs(
  INTERNAL_DB_CONNECTION,
  (): TypeOrmModuleOptions => ({
    name: INTERNAL_DB_CONNECTION,
    type: process.env.DATABASE_CLIENT as any,
    host: process.env.DATABASE_HOST,
    port: +process.env.DATABASE_PORT,
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    schema: process.env.DATABASE_SCHEMA,
    entities: [__dirname + '/../internal-db/models/*{.ts,.js}'],
    synchronize: false,
    migrationsTableName: 'migrations',
    migrations: [__dirname + '/../internal-db/migrations/*{.ts,.js}'],
    migrationsRun: false,
    cli: {
      migrationsDir: __dirname + '/../internal-db/migrations',
      entitiesDir: __dirname + '/../internal-db/models',
    },
    maxQueryExecutionTime: 20000,
    retryAttempts: 3,
    retryDelay: 1000,
  }),
);
