import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { configuration, ConfigValidationSchema } from './config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { dataDb as dataDbConfig } from '@config/data-db';
import { internalDb as internalDbConfig } from '@config/internal-db';
import { AppConfig } from '@common/providers/app-config.provider';
import { ContractScoreModule } from '@modules/contract-score/contract-score.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from '@modules/user/user.module';
import { DATA_DB_CONNECTION, INTERNAL_DB_CONNECTION } from '@config/tokens';
import { EmailModule } from '@modules/email/email.module';
import { PaymentModule } from '@modules/payment/payment.module';
import { PaymentGatewayModule } from '@modules/payment-gateway/payment-gateway.module';
import { RawBodyMiddleware } from '@root/raw-body.middleware';
import { JsonBodyMiddleware } from '@root/json-body.middleware';
import { PaymentCryptoModule } from '@modules/payment-crypto/payment-crypto.module';
import { PqrModule } from '@modules/pqr/pqr.module';
import { ChainScoreModule } from '@modules/chain-score/chain-score.module';
import { ContractScoreFavouritesModule } from '@modules/contract-score-favourites/contract-score-favourites.module';
import { ScheduleModule } from '@nestjs/schedule';
import { AdminModule } from '@modules/admin/admin.module';
import { ContractScoreCountModule } from '@modules/contract-score-count/contract-score-count.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: configuration,
      validationSchema: ConfigValidationSchema,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      name: DATA_DB_CONNECTION,
      useFactory: () => dataDbConfig(),
    }),
    TypeOrmModule.forRootAsync({
      name: INTERNAL_DB_CONNECTION,
      useFactory: () => internalDbConfig(),
    }),
    ContractScoreModule,
    ContractScoreCountModule,
    ContractScoreFavouritesModule,
    PqrModule,
    ChainScoreModule,
    UserModule,
    AdminModule,
    EmailModule.forRoot({
      host: process.env.MAIL_HOST,
      port: +process.env.MAIL_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.MAIL_USERNAME,
        pass: process.env.MAIL_PASSWORD,
      },
    }),
    PaymentGatewayModule.forRoot({
      apiKey:
        process.env.APP_ENV === 'development'
          ? process.env.DEV_STRIPE_API_KEY || process.env.STRIPE_API_KEY
          : process.env.STRIPE_API_KEY,
    }),
    PaymentCryptoModule.forRoot({
      CRYPTO_DEFISAFETY_CONTRACT_ADDRESS:
        process.env.CRYPTO_DEFISAFETY_CONTRACT_ADDRESS,
      CRYPTO_ETH_USDT_ADDRESS: process.env.CRYPTO_ETH_USDT_ADDRESS,
      CRYPTO_ETH_USDC_ADDRESS: process.env.CRYPTO_ETH_USDC_ADDRESS,
      CRYPTO_ETH_DAI_ADDRESS: process.env.CRYPTO_ETH_DAI_ADDRESS,
      CRYPTO_ARB_USDT_ADDRESS: process.env.CRYPTO_ARB_USDT_ADDRESS,
      CRYPTO_ARB_USDC_ADDRESS: process.env.CRYPTO_ARB_USDC_ADDRESS,
      CRYPTO_ARB_DAI_ADDRESS: process.env.CRYPTO_ARB_DAI_ADDRESS,
      CRYPTO_ETH_PROVIDER: process.env.CRYPTO_ETH_PROVIDER,
      CRYPTO_ARB_PROVIDER: process.env.CRYPTO_ARB_PROVIDER,
    }),
    PaymentModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [AppService, AppConfig],
})
export class AppModule implements NestModule {
  public configure(consumer: MiddlewareConsumer): void {
    consumer
      .apply(RawBodyMiddleware)
      .forRoutes({
        // Stripe webhook require raw body (to verify signature)
        path: '/payment/webhook',
        method: RequestMethod.POST,
      })
      .apply(JsonBodyMiddleware)
      .forRoutes('*');

    // consumer.apply(TraceMiddleware).forRoutes('*');
  }
}
