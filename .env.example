#Application port
PORT=3000
FRONT_END_HOST=http://localhost:3128
STRAPI_HOST=https://dev.defisafety.com
X_APPLICATION_KEY=904dab41-2677-42dc-b9a4-d3b464de530e
NODE_ENV=local

#Admin
ADMIN_API_KEY=b2fed32d-4812-43eb-9fee-57cdab5255b6

# Readonly contract-score database (MSSQL)
DATA_DB_HOST=localhost
DATA_DB_PORT=1433
DATA_DB_USER=SA
DATA_DB_PASSWORD=Secret1234
DATA_DB_NAME=defisafety

# Internal app database
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=defisafety
DATABASE_SCHEMA=defisafety

#Auth
JWT_ACCESS_TOKEN_SECRET=auth_secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=120
JWT_REFRESH_TOKEN_SECRET=refresh_secret
JWT_REFRESH_TOKEN_EXPIRATION_TIME=604800

#Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=admin
MAIL_PASSWORD=admin

#Monitoring
SENTRY_DSN=https://<EMAIL>/6291714

#Payment gateway
STRIPE_API_KEY=sk_test_51KgRrnB29B3waVVF5rQ17LkkYBfTE2H2ycRgehFYfMVwSuSeSdpKN4qxwR2yK1a19Fku1iRUjhD1gsZMqfdrRZw100Wr0nFCaM
STRIPE_WEBHOOK_SECRET=whsec_4FnpqPr7mr5dL4cvKhHT50jRM3oTafaB

#Crypto addresses
CRYPTO_DEFISAFETY_CONTRACT_ADDRESS=******************************************
CRYPTO_ETH_USDT_ADDRESS=******************************************
CRYPTO_ETH_USDC_ADDRESS=******************************************
CRYPTO_ETH_DAI_ADDRESS=******************************************
CRYPTO_ARB_USDT_ADDRESS=******************************************
CRYPTO_ARB_USDC_ADDRESS=******************************************
CRYPTO_ARB_DAI_ADDRESS=******************************************

#JSON-PRC API Providers (could be alchemy, infura etc.)
CRYPTO_ETH_PROVIDER=https://eth-rinkeby.alchemyapi.io/v2/********************************
CRYPTO_ARB_PROVIDER=https://arb-rinkeby.g.alchemy.com/v2/********************************
