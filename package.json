{"name": "defi-safety-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug=0.0.0.0:9229 --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage --maxWorkers=1", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "internalDb:migration:run": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -c internalDb migration:run", "internalDb:migration:revert": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -c internalDb migration:revert"}, "dependencies": {"@commitlint/cli": "^15.0.0", "@commitlint/config-conventional": "^15.0.0", "@nestjs/axios": "0.0.3", "@nestjs/common": "^8.4.2", "@nestjs/config": "^1.2.1", "@nestjs/core": "^8.4.2", "@nestjs/jwt": "^8.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^8.2.1", "@nestjs/platform-express": "^8.4.2", "@nestjs/schedule": "^1.1.0", "@nestjs/swagger": "^5.1.5", "@nestjs/typeorm": "^8.0.2", "@ntegral/nestjs-sentry": "^3.0.7", "@sentry/node": "^6.19.2", "@sentry/tracing": "^6.19.2", "@sentry/types": "^6.19.2", "@types/ejs": "^3.1.0", "@types/express": "^4.17.17", "@types/nodemailer": "^6.4.4", "@types/sharp": "^0.29.5", "axios": "^0.26.1", "body-parser": "^1.20.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "ejs": "^3.1.6", "ethereum-input-data-decoder": "^0.4.1", "express": "^4.17.3", "joi": "^17.5.0", "mssql": "^8.0.2", "nodemailer": "^6.7.3", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.7.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "sharp": "^0.29.3", "stripe": "^8.213.0", "swagger-ui-express": "^4.3.0", "ts-node": "^10.7.0", "tsc-files": "^1.1.3", "tsconfig-paths": "^3.14.1", "typeorm": "^0.2.45", "typeorm-naming-strategies": "^2.0.0", "uuid": "^8.3.2", "web3": "^1.7.1", "@types/passport": "^1.0.12", "@types/express-serve-static-core": "^4.17.31"}, "devDependencies": {"@nestjs/cli": "^8.2.5", "@nestjs/schematics": "^8.0.8", "@nestjs/testing": "^8.4.2", "@types/cron": "^1.7.3", "@types/jest": "^29.5.3", "@types/node": "^18.16.18", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.16.0", "@typescript-eslint/parser": "^5.16.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "husky": ">=6", "jest": "^27.2.5", "lint-staged": "^12.3.7", "prettier": "^2.6.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.8", "ts-node": "^10.7.0", "tsconfig-paths": "^3.14.1", "typescript": "^4.9.5"}, "resolutions": {"@types/express": "4.17.17", "@types/express-serve-static-core": "4.17.31", "@types/passport": "1.0.12"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^@root/(.*)": "<rootDir>/$1", "^@config/(.*)": "<rootDir>/config/$1", "^@common/(.*)": "<rootDir>common/$1", "^@modules/(.*)": "<rootDir>/modules/$1"}, "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "coverageThreshold": {"global": {"branches": 50, "functions": 50, "lines": 50, "statements": 50}}, "testEnvironment": "node", "coveragePathIgnorePatterns": ["node_modules", "<rootDir>/internal-db/migrations"], "testTimeout": 13000}, "lint-staged": {"*.ts": ["eslint --color --cache --max-warnings 0 --no-ignore"], "**/*.ts": "tsc-files --noEmit", "*.{ts,json,md}": ["prettier --write"]}}