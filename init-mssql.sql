-- Initialize MSSQL database for DeFi Safety API
-- This script creates the database and basic structure for contract-score data

-- Create the defisafety database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'defisafety')
BEGIN
    CREATE DATABASE defisafety;
END
GO

USE defisafety;
GO

-- Create basic tables for contract score data
-- Note: These are placeholder tables - you may need to adjust based on your actual schema

-- Table for score history
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='score_history' AND xtype='U')
BEGIN
    CREATE TABLE score_history (
        id INT IDENTITY(1,1) PRIMARY KEY,
        contract_address NVARCHAR(255) NOT NULL,
        score DECIMAL(5,2),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Table for searchable contracts
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='searchable' AND xtype='U')
BEGIN
    CREATE TABLE searchable (
        id INT IDENTITY(1,1) PRIMARY KEY,
        contract_address NVARCHAR(255) NOT NULL,
        name NVARCHAR(255),
        description NVARCHAR(MAX),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Insert some sample data for testing
INSERT INTO score_history (contract_address, score) VALUES 
    ('0x1234567890123456789012345678901234567890', 85.5),
    ('0xabcdefabcdefabcdefabcdefabcdefabcdefabcd', 92.3);

INSERT INTO searchable (contract_address, name, description) VALUES 
    ('0x1234567890123456789012345678901234567890', 'Sample Contract 1', 'A sample DeFi contract for testing'),
    ('0xabcdefabcdefabcdefabcdefabcdefabcdefabcd', 'Sample Contract 2', 'Another sample DeFi contract');

GO
