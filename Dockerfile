FROM node:18.0.0-alpine As development

# requirements
RUN apk update && apk add curl bash && rm -rf /var/cache/apk/*

# install node-prune (https://github.com/tj/node-prune)
RUN curl -sfL https://install.goreleaser.com/github.com/tj/node-prune.sh | bash -s -- -b /usr/local/bin

WORKDIR /usr/src/app

COPY package.json ./

# install dependencies
RUN yarn install

COPY . .

# fix permissions for node_modules/.bin
RUN chmod +x node_modules/.bin/*

# build application
RUN yarn run build

# remove development dependencies
#RUN npm prune --production

# run node prune
#RUN /usr/local/bin/node-prune

FROM node:18.0.0-alpine as production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

# copy from build image
COPY --from=development /usr/src/app/dist ./dist
COPY --from=development /usr/src/app/node_modules ./node_modules

# following files are required to run migrations
COPY . .

# fix permissions for node_modules/.bin in production stage
RUN chmod +x node_modules/.bin/*

ENTRYPOINT ["/bin/sh", "-c" , "yarn run internalDb:migration:run && node dist/src/main"]
